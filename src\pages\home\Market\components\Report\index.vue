<template>
  <div
    v-if="largeMarketList.length > 0"
    class="mb-16rpx mt-14rpx border-rd-16rpx bg-white px-[2rpx] py-[4rpx]"
  >
    <swiper
      autoplay
      disable-touch
      circular
      vertical
      :touchable="false"
      class="h-44rpx border-rd-t-16rpx bg-#E1F5FF py-[14rpx]"
    >
      <swiper-item v-for="item in latestNewList" :key="item.id">
        <LatestNew :data="item" />
      </swiper-item>
    </swiper>

    <div class="mt-[-8rpx] border-rd-t-16rpx bg-white px-[16rpx] py-[24rpx]">
      <div v-if="largeMarketList.length > 0">
        <nut-row type="flex" align="center" justify="space-between" gutter="5">
          <nut-col span="12">
            <Title title="大盘指数环比" @click="toLarge" />
          </nut-col>
          <nut-col span="11">
            <div class="f-c border-rd-36rpx bg-#F4F5F7 px-[4rpx] py-[4rpx]">
              <div
                v-for="item in firstClassList"
                :key="item.dictDetailCode"
                class="h-34rpx w-104rpx border-rd-36rpx text-center text-22rpx text-#878C94 line-height-34rpx"
                :class="
                  item.dictDetailCode === firstClassCode ? acvtiveClass : ''
                "
                @click="onClick(item)"
              >
                {{ item.dictDetailName }}
              </div>
            </div>
          </nut-col>
        </nut-row>
        <swiper
          disable-touch
          circular
          autoplay
          :touchable="false"
          :interval="10000"
          class="mt-42rpx h-210rpx"
          :current="currentIdx"
          @animationfinish="onChange"
        >
          <swiper-item v-for="(item, index) in largeMarketList" :key="index">
            <div
              v-if="currentIdx === index"
              class="f-c justify-between px-42rpx"
            >
              <Large
                v-for="largeItem in item.marketData"
                :key="largeItem.id"
                :data="largeItem"
                :current="currentIdx"
              />
            </div>
            <div v-else class="f-c justify-between px-42rpx">
              <Large
                v-for="largeItem in item.marketData"
                :key="largeItem.id"
                :data="largeItem"
              />
            </div>
          </swiper-item>
        </swiper>
      </div>

      <div v-if="quoteList.length > 0">
        <Title title="期货采购价格指数" @click="toAllNews" />
        <nut-row
          custom-class="mt-14rpx"
          type="flex"
          :gutter="3"
          justify="start"
          align="center"
        >
          <nut-col v-for="item in quoteList" :key="item.id" :span="8">
            <Quote :data="item" />
          </nut-col>
        </nut-row>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { NewsAppTypes, PlatformTypes, QueryBackendTypes } from "@/api";
import type { SwiperOnChangeEvent } from "@uni-helper/uni-app-types";
import Large from "./Large.vue";
import LatestNew from "./LatestNew.vue";
import Quote from "./Quote.vue";
import Title from "./Title.vue";

interface ILargeSwiperItem {
  dictDetailCode?: string;
  dictDetailName?: string;
  marketData?: NewsAppTypes.ILargeMarketDataH5Vo[];
  quoteData?: PlatformTypes.IPriceSituationAvgH5Vo[];
}

const LARGE_MARKET_CODE = "LARGE_MARKET_FIRST_CODE";
const KEY_HOME_LARGE_MARKET = "homeLargeMarket";

const latestNewList = ref<QueryBackendTypes.IArticleVo[]>([]);
const firstClassList = ref<NewsAppTypes.IDictDetailVo[]>([]);
const firstClassCode = ref<string>();
const currentIdx = ref<number>(0);
const largeMarketList = ref<ILargeSwiperItem[]>([]);
const acvtiveClass = "bg-#fff color-#33373f font-500";
const { openWebview } = useNavigate();

const quoteList = computed(() => {
  return (
    largeMarketList.value.filter(
      item => item.dictDetailCode === firstClassCode.value,
    )[0]?.quoteData ?? []
  );
});

const [fetchLatestNews] = useApiFetch(() =>
  queryBackendApi
    .ApiHomepageTodayquotesLatestList5newsorresearchreportGet()
    .then(rsp => {
      latestNewList.value = rsp.data;
    }),
);

async function fetchQuotes(item: NewsAppTypes.IDictDetailVo) {
  try {
    const rsp = await platformApi.ApiPriceSituationHomepageFluctuatingmaxPost({
      goodsTypeCode: item.dictDetailCode,
    });
    return {
      ...item,
      quoteData: rsp.data?.list ?? [],
    };
  } catch (e) {
    console.error(e);
  }
}

const [fetchFirstClassCode] = useApiFetch(() =>
  newsAppApi.ApiLargeMarketLargemarketfirstcodeListGet().then(async rsp => {
    firstClassList.value = rsp.data?.slice(0, 3) ?? [];
    firstClassCode.value = firstClassList.value[0]?.dictDetailCode ?? "";

    const list: ILargeSwiperItem[] = [];
    for (let i = 0; i < firstClassList.value.length; i++) {
      const item = firstClassList.value[i];
      const data = (await fetchLargeMarket(item)) ?? {};
      const quoteData = (await fetchQuotes(item)) ?? {};
      list.push({ ...data, ...quoteData });
    }
    largeMarketList.value = list;
    uni.setStorageSync(
      KEY_HOME_LARGE_MARKET,
      JSON.stringify(largeMarketList.value),
    );
  }),
);

async function fetchLargeMarket(item: NewsAppTypes.IDictDetailVo) {
  try {
    const rsp = await newsAppApi.ApiLargeMarketListbyfirstsecondclassPost({
      // @ts-ignore
      firstClassCode: item.dictDetailCode,
    });

    return {
      ...item,
      marketData: rsp.data?.slice(0, 3) ?? [],
    };
  } catch (e) {
    console.error(e);
  }
}

function onClick(item: NewsAppTypes.IDictDetailVo) {
  firstClassCode.value = item.dictDetailCode;
  currentIdx.value = firstClassList.value.findIndex(
    i => i.dictDetailCode === item.dictDetailCode,
  );
}

function onChange(e: SwiperOnChangeEvent) {
  const { current } = e.detail;
  currentIdx.value = current;
  firstClassCode.value = firstClassList.value[current]?.dictDetailCode ?? "";
}

function tryInitLargeStroage(): ILargeSwiperItem[] {
  const storageData = uni.getStorageSync(KEY_HOME_LARGE_MARKET);
  if (storageData) {
    try {
      largeMarketList.value = JSON.parse(storageData);
    } catch {}
  }

  return [];
}

function toAllNews() {
  console.log("toAllNews");
  openWebview({
    url: "/youdingte-news-h5/home?type=PRICE",
    autoOrigin: true,
  });
}

function toLarge() {
  openWebview({
    url: "/youdingte-news-h5/home?type=DAPAN",
    autoOrigin: true,
    withToken: true,
  });
}

function fetchAll() {
  fetchLatestNews();
  fetchFirstClassCode();
}

defineExpose({
  fetchAll,
});

onBeforeMount(() => {
  tryInitLargeStroage();
  fetchAll();
});
</script>
