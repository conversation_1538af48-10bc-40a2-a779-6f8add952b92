<template>
  <div class="ai-bubble">
    <div class="ai-bubble-title">{{ title }}</div>
    <div class="ai-bubble-content">
      <template v-for="(chunk, index) in chunks" :key="chunk.id">
        <span :class="{ 'typing-new': index === chunks.length - 1 }">{{
          chunk.text
        }}</span>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  text: string;
  title?: string;
  blurTyping?: boolean; // 是否雾化未打字部分
}

const props = withDefaults(defineProps<Props>(), {
  title: "正在分析...",
  blurTyping: false,
});

const chunks = ref<{ text: string; id: number }[]>([]);
let chunkId = 0;

watch(
  () => props.text,
  (newVal, oldVal) => {
    if (newVal && oldVal && newVal.length > oldVal.length) {
      // 获取新增的文本
      const newChunk = newVal.slice(oldVal.length);
      // 添加新chunk
      chunks.value.push({
        text: newChunk,
        id: chunkId++,
      });
    } else {
      // 初始文本或重置
      chunks.value = newVal
        ? [
            {
              text: newVal,
              id: chunkId++,
            },
          ]
        : [];
    }
  },
  { immediate: true },
);
</script>

<style lang="scss" scoped>
.ai-bubble {
  margin-bottom: 16rpx;
  color: #999;
  word-break: break-word;
  border-radius: 28rpx;

  .ai-bubble-title {
    height: 60rpx;
    font-size: 28rpx;
    line-height: 60rpx;
  }

  .ai-bubble-content {
    position: relative;
    padding-left: 22rpx;
    font-size: 26rpx;
    font-weight: 400;
    line-height: 44rpx;
    white-space: pre-wrap;

    &::before {
      position: absolute;
      top: 10rpx;
      bottom: 10rpx;
      left: 0;
      width: 4rpx;
      content: "";
      background: #d8d8d8;
    }
  }
}
</style>
