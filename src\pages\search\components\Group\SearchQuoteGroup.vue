<template>
  <div class="mb-20px">
    <SearchResultTilte show-more-btn @more-click="toQuoteListPage">
      {{ $t("hang-qing-jia-ge") }}
    </SearchResultTilte>
    <div class="m-20rpx rounded-card py-10rpx">
      <div
        v-for="quote in list"
        :key="quote.id"
        class="search-quote-item"
        @click="toDetail(quote)"
      >
        <nut-row type="flex" align="center" justify="space-between">
          <nut-col :span="8">
            <div class="text-left text-30rpx color-black-trade font-500">
              {{ quote.productName ?? " --" }}
            </div>
            <div
              v-if="quote.countryName"
              class="mt-8rpx text-left text-24rpx color-grey"
            >
              {{ quote.countryName }}
            </div>
          </nut-col>
          <nut-col :span="5"> {{ quote.avgPrice ?? "--" }} </nut-col>
          <nut-col :span="5">
            <div :style="{ color: quote.trend.color }">
              {{ quote.trend.toValueString() }}
            </div>
          </nut-col>
          <nut-col :span="5">
            <div
              class="rounded-2px py-8rpx text-24rpx c-white"
              :style="{ background: quote.trend.color }"
            >
              {{ quote.trend.toRatePercent() }}
            </div>
          </nut-col>
        </nut-row>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { PlatformTypes } from "@/api";
import SearchResultTilte from "../SearchResultTilte.vue";

const props = defineProps<{
  quotes?: PlatformTypes.IPriceSituationAvgH5Vo[];
}>();

const { openWebview } = useNavigate({ webview: { autoOrigin: true } });

const { quotes } = toRefs(props);

const list = computed(() =>
  quotes.value?.map(item => {
    return {
      ...item,
      trend: useQuoteTrend(item.riseFallRate, item.riseFallValue),
    };
  }),
);

function toQuoteListPage() {
  openWebview({
    url: "/youdingte-news-h5/quote-list",
  });
}

function toDetail(item: PlatformTypes.IPriceSituationAvgH5Vo) {
  openWebview({
    url: `/youdingte-news-h5/price-detail/${item.id}`,
  });
}
</script>

<style lang="scss" scoped>
.search-quote-item {
  @apply flex items-start justify-between gap-20rpx mx-20rpx  py-20rpx;

  font-size: 26rpx;
  font-weight: 400;
  text-align: center;
  border-bottom: 1px solid theme("colors.grey.light");

  &:last-child {
    border-bottom: none;
  }
}
</style>
