<template>
  <div class="box-border flex flex-col items-center px-60rpx pt-148rpx">
    <image :src="LogoIcon" class="mb-72rpx block h-120rpx w-120rpx" />
    <image :src="XiaoyouTextIcon" class="block h-50rpx w-250rpx" />

    <div
      class="mb-34rpx mt-26rpx text-center text-28rpx c-#33373F font-400 lh-44rpx"
    >
      {{ xyTips.tips }}
    </div>

    <!-- <div class="flex items-center">
      <image :src="LabelIcon" class="h-40rpx w-40rpx" />
      <div class="mx-4rpx h-34rpx text-24rpx c-#999999 lh-34rpx">
        当提问次数达到今日上限，将无法继续提问
      </div>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import XiaoyouTextIcon from "@/static/chat/xiaoyou-text.png";
import LogoIcon from "@/static/logo.png";
import { useChatStore } from "@/store/modules/chat";
import { useSession } from "../composations/useSession";

const chatStore = useChatStore();
const { xyTips } = storeToRefs(chatStore);

const { queryMessage } = useSession();

const currentPage = ref(0);

function handleRefresh() {
  currentPage.value++;
}

function handleClickQuestion(question?: string) {
  if (!question) return;
  queryMessage({ prompt: question });
}
</script>

<style lang="scss" scoped>
.reverse {
  transform: rotateY(180deg);
}
</style>
