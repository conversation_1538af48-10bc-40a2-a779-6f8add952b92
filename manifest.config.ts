/* eslint-disable style/quotes */
import { defineManifestConfig } from "@uni-helper/vite-plugin-uni-manifest";

export default defineManifestConfig({
  name: "youdingte-weapp",
  appid: "",
  description: "",
  versionName: "1.0.0",
  versionCode: "100",
  transformPx: false,
  "mp-weixin": {
    appid: "wx792988070c9973e8",
    setting: {
      urlCheck: false,
      es6: true,
      minified: true,
      postcss: true,
      ignoreDevUnusedFiles: false,
      ignoreUploadUnusedFiles: false,
    },
    usingComponents: true,
    lazyCodeLoading: "requiredComponents",
    darkmode: false,
    themeLocation: "theme.json",
    libVersion: "3.4.3",
    requiredPrivateInfos: ["getLocation"],
    permission: {
      "scope.userLocation": { desc: "你的位置信息将用于匹配适合你的数据" },
      "scope.userLocationBackground": {
        desc: "",
      },
      "scope.userFuzzyLocation": {
        desc: "",
      },
    },
  },
  /* 5+App特有相关 */
  "app-plus": {
    usingComponents: true,
    nvueStyleCompiler: "uni-app",
    compilerVersion: 3,
    splashscreen: {
      alwaysShowBeforeRender: true,
      waiting: true,
      autoclose: true,
      delay: 0,
    },
    /* 模块配置 */
    modules: {
      /* 使用Canvas模块，需要添加下面这一行 */
      Canvas: "nvue canvas",
    },
    /* 应用发布信息 */
    distribute: {
      /* android打包配置 */
      android: {
        permissions: [
          '<uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>',
          '<uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"/>',
          '<uses-permission android:name="android.permission.VIBRATE"/>',
          '<uses-permission android:name="android.permission.READ_LOGS"/>',
          '<uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>',
          '<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>',
          '<uses-permission android:name="android.permission.CAMERA"/>',
          '<uses-permission android:name="android.permission.GET_ACCOUNTS"/>',
          '<uses-permission android:name="android.permission.READ_PHONE_STATE"/>',
          '<uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>',
          '<uses-permission android:name="android.permission.WAKE_LOCK"/>',
          '<uses-permission android:name="android.permission.WRITE_SETTINGS"/>',
        ],
      },
      /* ios打包配置 */
      ios: {},
      /* SDK配置 */
      sdkConfigs: {},
    },
  },
  /* 快应用特有相关 */
  quickapp: {},
  /* 小程序特有相关 */
  "mp-alipay": {
    usingComponents: true,
  },
  "mp-baidu": {
    usingComponents: true,
  },
  "mp-toutiao": {
    usingComponents: true,
  },
  h5: {
    darkmode: true,
    themeLocation: "theme.json",
    router: {
      mode: "history",
      base: "/",
    },
  },
  uniStatistics: {
    enable: false,
  },
  vueVersion: "3",
});
