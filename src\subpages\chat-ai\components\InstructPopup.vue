<template>
  <nut-popup
    v-model:visible="showBottom"
    position="bottom"
    round
    :safe-area-inset-bottom="true"
    :custom-style="{ height: '30%' }"
  >
    <div class="px-30rpx py-64rpx">
      <div class="mb-26rpx h-50rpx text-36rpx c-#394253 font-500 lh-50rpx">
        常用命令
      </div>
      <template v-if="list.length">
        <div
          v-for="(item, index) in list"
          :key="index"
          class="mb-50rpx text-26rpx lh-36rpx last:mb-0"
        >
          <div class="mb-8rpx h-36rpx c-#394253 font-500">
            {{ item?.type }}
          </div>
          <div class="flex gap-12rpx">
            <div
              v-for="(subItem, subIndex) in item?.prompt_list"
              :key="subIndex"
              class="h-36rpx rounded-8rpx bg-#F4F5F7 px-20rpx py-12rpx c-#33373F"
              @click="handleLabel(subItem)"
            >
              {{ subItem.label }}
            </div>
          </div>
        </div>
      </template>
    </div>
  </nut-popup>
</template>

<script setup lang="ts">
import type * as ChatTypes from "@/api/servers/chat/type";
import { useChatStore } from "@/store/modules/chat";

const showBottom = defineModel<boolean>({ required: true });

const { xyTips, inputValue } = storeToRefs(useChatStore());

const list = computed(() => {
  return [xyTips.value.search_prompt, xyTips.value.oper_prompt];
});

function handleLabel(item: ChatTypes.PromptList) {
  showBottom.value = false;
  inputValue.value = item.prompt;
}
</script>
