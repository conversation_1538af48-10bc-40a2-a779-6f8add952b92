<template>
  <nut-config-provider :theme-vars="nutTheme">
    <PageHome
      v-if="!isNil(homePageTab) && loadedPages.includes(NavigationTabEnum.Home)"
      v-show="currentTab === NavigationTabEnum.Home"
      v-model="homePageTab"
    />
    <PageMessage
      v-if="loadedPages.includes(NavigationTabEnum.Message)"
      v-show="currentTab === NavigationTabEnum.Message"
      :bottom-tab="currentTab"
    />
    <PageMine
      v-if="loadedPages.includes(NavigationTabEnum.Mine)"
      v-show="currentTab === NavigationTabEnum.Mine"
    />
    <BottomNavigationBar v-model="currentTab" />
  </nut-config-provider>
</template>

<script setup lang="ts">
import { nutTheme } from "@/styles/themes/nut.theme";
import { isNil } from "lodash";
import { delay } from "oig-utils";
import PageHome from "../home/<USER>";
import { HomeTabEnum } from "../home/<USER>";
import PageMessage from "../message/index.vue";
import PageMine from "../mine/index.vue";
import BottomNavigationBar from "./components/BottomNavigationBar.vue";
import { NavigationTabEnum } from "./type";

const { openWebview } = useNavigate();
const loadedPages = ref<NavigationTabEnum[]>([NavigationTabEnum.Home]);
const currentTab = ref<NavigationTabEnum>(NavigationTabEnum.Home);
const homePageTab = ref<HomeTabEnum>();

onLoad(options => {
  homePageTab.value =
    options?.homeTab === HomeTabEnum.Porxy.toString()
      ? HomeTabEnum.Porxy
      : HomeTabEnum.Mall;
  if (options?.currentTab) {
    currentTab.value = Number(options?.currentTab);
    if (!loadedPages.value.includes(currentTab.value)) {
      loadedPages.value.push(currentTab.value);
    }
  }

  if (options?.[XHeader.HToken]) {
    useUserStore().relogin(options?.[XHeader.HToken]);
  }
});

watch(currentTab, async (newValue, oldValue) => {
  if (newValue === NavigationTabEnum.Report) {
    openWebview({
      url: "/youdingte-news-h5/home",
      autoOrigin: true,
      withToken: true,
    });
    await delay(1000);
    currentTab.value = oldValue;
    return;
  }

  if (!loadedPages.value.includes(newValue)) {
    loadedPages.value.push(newValue);
  }
});

onShow(() => {
  // 加载过我的页面，则需要重新更新商户列表
  if (loadedPages.value.includes(NavigationTabEnum.Mine)) {
    useUserStore().queryMerchantList();
    useUserStore().queryUserInfo();
  }
});

onShareAppMessage(options =>
  useShareAppMessage(options, {
    promise: new Promise(resolve =>
      resolve({
        title: defaultShareOptions.title,
        path: `/pages/index/index?homeTab=${homePageTab.value}`,
      }),
    ),
  }),
);
</script>
