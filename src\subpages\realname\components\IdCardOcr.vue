<template>
  <div class="flex items-center justify-between">
    <span class="text-14px text-black-trade">上传身份证照片</span>
    <div class="f-c gap-10px">
      <image
        class="h-28px w-65px"
        :src="'ex-icon.png'.toQiniuUrl()"
        mode="scaleToFill"
        @click="exampleClick"
      />
      <image
        class="size-28px"
        :src="'photo.png'.toQiniuUrl()"
        mode="scaleToFill"
        @click="pickImage"
      />
    </div>
  </div>

  <nut-popup
    v-model:visible="exampleVisible"
    :custom-style="{ borderRadius: '6px' }"
  >
    <div class="box-border flex-col p-20px">
      <image
        class="mb-28px h-183px w-280px"
        :src="'IdCardExample.png'.toQiniuUrl()"
        mode="aspectFit"
      />
      <div v-if="mode === 'business'" class="text">
        1、营业执照照片包含执照内所有信息，文字清晰，使用复印件需要加盖公章
      </div>
      <div v-else-if="mode === 'idcard'" class="text">
        1、身份证件照片默认大陆身份证，照片要求文字清晰，易于识别
      </div>
      <div class="text">2、建议上传能看清字体的清晰图片，保证识别通过率</div>
      <div class="btn" @click="pickImage">
        {{ uploadText }}
      </div>
    </div>
  </nut-popup>
</template>

<script setup lang="ts">
import type {
  ICommonBusinessLicenseResponseVo,
  ICommonIdcardFrontResponseVo,
} from "youxian-api/src/ocr/types";
import { useToggle } from "@vueuse/core";

const props = withDefaults(
  defineProps<{
    mode?: "idcard" | "business";
  }>(),
  {
    mode: "idcard",
  },
);
const emit = defineEmits<{
  (
    e: "businessSuccess",
    param: { data: ICommonBusinessLicenseResponseVo; url: string },
  ): void;
  (
    e: "idCardsuccess",
    param: { data: ICommonIdcardFrontResponseVo; url: string },
  ): void;
}>();
const uploadText = computed(() => {
  return props.mode === "idcard" ? "上传身份证照片" : "上传营业执照";
});

const [exampleVisible, setExampleVisible] = useToggle(false);
const { chooseImage, uploadSingleImage } = useUpload("ocr");

function exampleClick() {
  setExampleVisible(true);
}

async function pickImage() {
  setExampleVisible(false);

  const url = await chooseImage().then(uploadSingleImage);

  if (props.mode === "business") {
    ocrLicense(url);
  } else if (props.mode === "idcard") {
    ocrCard(url);
  }
}

function ocrLicense(url: string) {
  ocrApi
    .ApiOcrBusinesslicensePost({ url }, { showLoading: true, showError: true })
    .then(res => {
      const param = { data: res.data, url };
      emit("businessSuccess", param);
    });
}

function ocrCard(url: string) {
  ocrApi
    .ApiOcrFrontidcardPost({ url }, { showLoading: true, showError: true })
    .then(res => {
      const param = { data: res.data, url };
      emit("idCardsuccess", param);
      uni.hideLoading();
    });
}
</script>

<style lang="scss" scoped>
.text {
  @apply text-13px text-black-trade mb-10px;
}

.btn {
  @apply w-full text-16px text-primary text-center py-10px;
}
</style>
