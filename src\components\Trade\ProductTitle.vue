<template>
  <div class="line-clamp-2 break-all">
    <ProductTag :styled-text="styledText" class="relative top-[-3rpx]" />
    <span class="ml-10rpx color-black-trade font-500 leading-[46rpx]">
      <slot />
    </span>
  </div>
</template>

<script setup lang="ts">
import ProductTag from "./ProductTag/ProductTag.vue";

defineProps<{ styledText: ReturnType<typeof useTradeProduct>["styledText"] }>();
</script>
