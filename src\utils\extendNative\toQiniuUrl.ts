import { Config } from "@/config";

enum ThumbnailSize {
  small,
  middle,
}

function toQiniuUrl(
  this: string,
  options: { suffixPath: string } = { suffixPath: "/youdingte_weapp" },
): string {
  return `${Config.qiniuDownloadUrl}${options.suffixPath}/${this}`;
}

function toThumbnailUrl(
  this: string,
  size: ThumbnailSize = ThumbnailSize.middle,
) {
  if (!this) {
    return "";
  }

  if (this.includes("imageView2/")) {
    return this;
  }

  // eslint-disable-next-line ts/no-this-alias
  let url = this;
  const sizeQuery =
    size === ThumbnailSize.small
      ? "?imageView2/2/w/300"
      : "?imageView2/2/w/700";

  if (!this.startsWith("http")) {
    url = `${Config.qiniuDownloadUrl}/${this}`;
  } else if (this.startsWith("http://")) {
    url = this.replace("http://", "https://");
  }

  return url + sizeQuery;
}

export type ToQiniuUrlFunc = typeof toQiniuUrl;
export type ToThumbnailUrl = typeof toThumbnailUrl;

String.prototype.toQiniuUrl = toQiniuUrl;
String.prototype.toThumbnailUrl = toThumbnailUrl;
