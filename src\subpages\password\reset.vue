<template>
  <Password :submit="onSubmit" />
</template>

<script setup lang="ts">
import { delay } from "oig-utils";
import Password from "./index.vue";

useTitle(t("xiu-gai-mi-ma"));

async function onSubmit(
  formData: Parameters<InstanceType<typeof Password>["$props"]["submit"]>[0],
) {
  const rsp = await userApi.ApiUserUpdatepasswordPost(
    {
      password: formData.password,
      confirmPassword: formData.newPassword,
      smsCode: formData.code,
    },
    {
      showError: true,
    },
  );
  uni.navigateBack();
  await delay(300);
  showMsg(rsp.msg);
}
</script>
