<template>
  <div class="h-full w-full flex flex-col">
    <template v-if="render === 'input'">
      <!-- eslint-disable -->
      <input
        type="text"
        :value="modelValue"
        :class="commonClass"
        :show-confirm-bar="false"
        :adjust-position="true"
        :confirm-type="confirmType"
        :disabled="disabled"
        :placeholder="placeholder"
        placeholder-style="color:#94a3b8;"
        @confirm="handleConfirm"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @keyboardheightchange="handleKeyboardHeightChange"
      />
    </template>

    <template v-else-if="render === 'textarea'">
      <textarea
        :value="modelValue"
        :class="commonClass"
        class="transition-all duration-300 flex-grow min-h-0 pt-14rpx"
        :style="{ maxHeight: maxHeight }"
        :show-confirm-bar="false"
        :adjust-position="false"
        :confirm-type="confirmType"
        :disable-default-padding="true"
        :disabled="disabled"
        :placeholder="placeholder"
        placeholder-style="color:#94a3b8;"
        auto-height
        :maxlength="maxlength"
        :cursor-spacing="80"
        @confirm="handleConfirm"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @linechange="handleLinechange"
        @keyboardheightchange="handleKeyboardHeightChange"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
interface Props {
  modelValue?: string;
  placeholder?: string;
  render?: "input" | "textarea";
  confirmType?: "send" | "search" | "next" | "go" | "done";
  disabled?: boolean;
  maxHeight?: string;
  maxlength?: number;
}

withDefaults(defineProps<Props>(), {
  modelValue: "",
  placeholder: "请输入",
  render: "textarea",
  confirmType: "send",
  disabled: false,
  maxHeight: "208rpx",
  maxlength: -1,
});

const emit = defineEmits<{
  (e: "update:modelValue", value: string): void;
  (
    e: "input",
    event: UniHelper.InputOnInputEvent | UniHelper.TextareaOnInputEvent,
  ): void;
  (
    e: "focus",
    event: UniHelper.InputOnFocusEvent | UniHelper.TextareaOnFocusEvent,
  ): void;
  (
    e: "blur",
    event: UniHelper.InputOnBlurEvent | UniHelper.TextareaOnBlurEvent,
  ): void;
  (
    e: "confirm",
    event: UniHelper.InputOnConfirmEvent | UniHelper.TextareaOnConfirmEvent,
  ): void;
  (e: "linechange", event: UniHelper.TextareaOnLinechangeEvent): void;
  (
    e: "keyboardheightchange",
    event:
      | UniHelper.InputOnKeyboardheightchangeEvent
      | UniHelper.TextareaOnKeyboardheightchangeEvent,
  ): void;
}>();

const commonClass = "lh-40rpx max-h-208rpx w-full text-28rpx";

// 输入事件处理
function handleInput(
  event: UniHelper.InputOnInputEvent | UniHelper.TextareaOnInputEvent,
) {
  emit("update:modelValue", event.detail.value);
  emit("input", event);
}

// 获得焦点事件处理
function handleFocus(
  event: UniHelper.InputOnFocusEvent | UniHelper.TextareaOnFocusEvent,
) {
  emit("focus", event);
}

// 失去焦点事件处理
function handleBlur(
  event: UniHelper.InputOnBlurEvent | UniHelper.TextareaOnBlurEvent,
) {
  emit("blur", event);
}

// 点击完成事件处理
function handleConfirm(
  event: UniHelper.InputOnConfirmEvent | UniHelper.TextareaOnConfirmEvent,
) {
  emit("confirm", event);
}

// 输入框行数变化时调用
function handleLinechange(event: UniHelper.TextareaOnLinechangeEvent) {
  emit("linechange", event);
}

// 键盘高度变化事件处理
function handleKeyboardHeightChange(
  event:
    | UniHelper.InputOnKeyboardheightchangeEvent
    | UniHelper.TextareaOnKeyboardheightchangeEvent,
) {
  emit("keyboardheightchange", event);
}
</script>

<style scoped lang="scss">
.custom-input {
  width: 100% !important;

  .chat-input {
    width: 100% !important;
    max-height: 104px;
    font-size: 16px;
    line-height: 20px;
  }
}
</style>
