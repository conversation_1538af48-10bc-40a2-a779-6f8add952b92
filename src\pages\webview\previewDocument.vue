<template>
  <div class="h-100vh bg-white px-50rpx pt-80rpx text-center">
    <div class="mb-50rpx text-32rpx">{{ dowloadText }}</div>
    <nut-button
      v-if="downloadStatus === 'success'"
      type="primary"
      block
      size="large"
      shape="square"
      @click="openDocument"
    >
      打开文件
    </nut-button>
  </div>
</template>

<script setup lang="ts">
import { XHeader } from "@/api/constant";
import { decodeURIObject, parseFileFromUrl } from "@/utils/url";

useTitle("文件预览");
const downloadFailText = ref("");
const downloadStatus = ref<"downloading" | "fail" | "success">();
const dowloadText = computed(() => {
  const name = "文件";
  if (downloadStatus.value === "downloading")
    return `${name}下载中... 请勿离开页面`;
  if (downloadStatus.value === "fail")
    return `${name}下载失败: ${downloadFailText.value}`;
  if (downloadStatus.value === "success") return `${name}下载成功，可重新打开`;

  return "";
});

const fileState = reactive({
  downloadUrl: "",
  tempFilePath: "",
  fileName: "",
  fileType: "",
  withToken: false,
  showMenu: true,
});

onLoad(options => {
  const { url, fileName, fileType, withToken, showMenu } =
    decodeURIObject(options);

  if (url) {
    fileState.downloadUrl = url;
    const file = parseFileFromUrl(url);
    fileState.fileType = fileType || file.fileType;
    fileState.fileName = fileName || file.name;
    fileState.withToken = withToken === "true";
    fileState.showMenu = showMenu === "true";
    downloadFile().then(() => openDocument());
  }
});

function openDocument() {
  uni.openDocument({
    filePath: fileState.tempFilePath,
    showMenu: fileState.showMenu,
    fileType: fileState.fileType,
  });
}

function downloadFile() {
  downloadStatus.value = "downloading";
  const header: Record<string, string> = {};

  if (fileState.withToken) {
    header[XHeader.HToken] = useUserStore().token;
  }

  return new Promise(resolve => {
    if (fileState.downloadUrl) {
      uni.downloadFile({
        url: fileState.downloadUrl,
        header,
        success: res => {
          if (res.statusCode !== 200) {
            downloadFailText.value = `【${res.statusCode.toString()}】`;
            downloadStatus.value = "fail";
            return;
          }
          fileState.tempFilePath = res.tempFilePath;
          downloadStatus.value = "success";
          resolve(res.tempFilePath);
        },
        fail: () => {
          downloadStatus.value = "fail";
        },
      });
    }
  });
}
</script>
