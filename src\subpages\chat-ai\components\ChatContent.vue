<template>
  <div class="relative h-full">
    <scroll-view
      id="chat-content"
      scroll-y
      :enhanced="true"
      :enable-passive="true"
      :scroll-top="scrollTop"
      :scroll-with-animation="true"
      class="h-full bg-white"
      @scroll="onScroll"
    >
      <template v-if="messages.length === 0">
        <SystemDefaultData />
      </template>
      <div v-else class="pb-64rpx">
        <div v-for="(message, index) in messages" :key="message.id">
          <ChatItem
            :message="message"
            @update-message="val => (messages[index] = val)"
          />
        </div>
      </div>
    </scroll-view>
    <!-- 返回底部 -->
    <BackBottom
      v-show="!isAtBottom"
      class="absolute bottom-34rpx left-0 right-0 mx-auto w-70rpx"
      @back-bottom="backBottom"
    />
  </div>
</template>

<script setup lang="ts">
import { useChatStore } from "@/store/modules/chat";
import { useDebounceFn } from "@vueuse/core";
import { useElementRect } from "../composations/useElementRect";
import BackBottom from "./BackBottom.vue";
import ChatItem from "./ChatItem.vue";
import SystemDefaultData from "./SystemDefaultData.vue";

const { messages } = storeToRefs(useChatStore());
const { getRect } = useElementRect();
const scrollTop = ref(0);

const containerHeight = ref<number>(0);
const handleScrollToBottom = useDebounceFn(() => {
  getRect("#chat-content").then(rect => {
    containerHeight.value =
      (Array.isArray(rect) ? rect[0].height : rect.height) || 0;
    scrollTop.value =
      (Array.isArray(rect) ? rect[0].scrollHeight : rect.scrollHeight) || 0;
  });
}, 20);

watch(messages, handleScrollToBottom, { immediate: true, deep: true });

// 判断是否在底部，允许2px误差
const isAtBottom = ref<boolean>(true);
function onScroll(e: UniHelper.ScrollViewOnScrollEvent) {
  handleScrollToBottom();
  const { scrollTop, scrollHeight } = e.detail;
  isAtBottom.value = scrollTop + containerHeight.value >= scrollHeight - 2;
}

function backBottom() {
  scrollTop.value = Date.now();
}
</script>
