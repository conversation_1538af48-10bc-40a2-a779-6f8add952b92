<template>
  <BackgroundLayout
    :background="activeTab === HomeTabEnum.Mall ? 'HOME' : 'PROXY'"
  >
    <HomeTab v-model="activeTab" @change="onTabChanged" />
    <view class="px-[16rpx]">
      <HomeSearchBar
        :height="searchBarHeight"
        :tab="activeTab"
        @click="toSearchPage"
      />
      <MarketPage
        v-if="loadedTabs.includes(HomeTabEnum.Mall)"
        v-show="activeTab === HomeTabEnum.Mall"
      />
      <ProxyPage
        v-if="loadedTabs.includes(HomeTabEnum.Porxy)"
        v-show="activeTab === HomeTabEnum.Porxy"
      />
    </view>

    <ActivePopover />
  </BackgroundLayout>
</template>

<script setup lang="ts">
import ActivePopover from "./components/ActivePopover.vue";
import HomeSearchBar from "./components/HomeSearchBar.vue";
import HomeTab from "./components/HomeTab.vue";
import MarketPage from "./Market/Market.vue";
import ProxyPage from "./Proxy/Proxy.vue";
import { HomeTabEnum } from "./useHomeView";

onMounted(() => {
  preloadHomeImages();
});

const activeTab = defineModel<HomeTabEnum>({ required: true });
const { openWebview, withLogin } = useNavigate({
  webview: { withToken: true, autoOrigin: true },
});

const loadedTabs = ref([activeTab.value]);
const searchBarHeight = "120rpx";

function onTabChanged(tab: HomeTabEnum) {
  if (!loadedTabs.value.includes(tab)) {
    loadedTabs.value.push(tab);
  }
}

function toSearchPage() {
  if (activeTab.value === HomeTabEnum.Mall) {
    uni.navigateTo({
      url: "/pages/search/history",
    });
  } else {
    withLogin().run(() => {
      openWebview({ url: "/proxy-h5/query/index" });
    });
  }
}
</script>
