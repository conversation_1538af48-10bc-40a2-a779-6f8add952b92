<template>
  <nut-searchbar
    v-model="value"
    :autofocus="autofocus"
    :placeholder="$t('searchPlaceholder')"
    @search="val => emit('search', val)"
    @click-input="val => emit('clickInput', val)"
  >
    <template #leftin>
      <nut-icon name="search2" />
    </template>
    <template #rightout>
      <div @click="back">取消</div>
    </template>
  </nut-searchbar>
</template>

<script setup lang="ts">
withDefaults(
  defineProps<{
    autofocus?: boolean;
  }>(),
  {
    autofocus: false,
  },
);

const emit = defineEmits<{
  search: [value: string];
  clickInput: [value: string];
}>();
const value = defineModel<string>({ required: true });

function back() {
  uni.navigateBack();
}
</script>
