<template>
  <div
    class="relative bottom-12rpx left-12rpx box-border inline-block rounded-20rpx bg-white"
    @click="handlePreview"
  >
    <div class="h-128rpx w-128rpx shrink-0 rounded-8rpx">
      <image
        v-if="loading"
        :src="LoadingIcon"
        class="absolute inset-0 h-full w-full"
        alt=""
      />
      <image
        :src="url"
        mode="aspectFill"
        class="h-full w-full"
        alt=""
        @load="handleImageLoad"
      />
      <div
        class="absolute z-1 -right-12rpx -top-12rpx"
        @click.stop="() => emits('delete')"
      >
        <image :src="DeleteIcon" class="h-44rpx w-44rpx" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import DeleteIcon from "@/static/delete-solid.svg";
import LoadingIcon from "@/static/loading.svg";

interface Props {
  url: string;
}

const { url } = defineProps<Props>();
const emits = defineEmits<{ delete: [] }>();
const loading = ref(true);

function handlePreview() {
  uni.previewImage({
    urls: [url],
  });
}

function handleImageLoad() {
  loading.value = false;
}
</script>
