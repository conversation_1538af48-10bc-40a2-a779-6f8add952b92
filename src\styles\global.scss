
@import './nut.reset';

html,
body,
#app,
page {
  height: 100%;
  padding: 0;
  margin: 0;
  color: $textColor;
  background: $bgColor;
}


.disable-label {
  box-sizing: border-box;
  width: 92rpx;
  height: 36rpx;
  font-size: 22rpx;
  line-height: 36rpx;
  color: #878c94;
  text-align: center;
  background:#F4F5F7;
  border: 1rpx solid #b8b9ba;
  border-radius: 4rpx;
}

.green-label {
  box-sizing: border-box;
  width: 92rpx;
  height: 36rpx;
  font-size: 22rpx;
  line-height: 36rpx;
  color: #fff;
  text-align: center;
  background-color: #3db97f;
  border-radius: 4rpx;
}

.plain-blue-label {
  box-sizing: border-box;
  width: 92rpx;
  height: 36rpx;
  font-size: 22rpx;
  line-height: 34rpx;
  color: #0278FF;
  text-align: center;
  background: #EDF5FF;
  border: 1rpx solid #0278FF;
  border-radius: 4rpx;
}


.typing-new {
  animation: fadeIn 0.3s ease-out forwards;
}

@keyframes fadeIn {
  from { opacity: 0 }

  to {
    opacity: 1;
  }
}
