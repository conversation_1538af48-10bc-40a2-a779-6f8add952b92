<template>
  <TitleCard :title="$t('dai-li')">
    <template #title-right>
      <div
        class="text-26rpx color-grey"
        @click="
          () =>
            openWebview({ url: '/proxy-h5/pages/order/index?orderStatus=all' })
        "
      >
        {{ $t("quan-bu") }}
      </div>
    </template>

    <div class="flex justify-around py-16rpx">
      <EntityItem
        v-for="item in entity"
        :key="item.name"
        :icon="item.icon"
        :name="item.name"
        :num="item.badge"
        @click="() => openWebview({ url: item.url })"
      />
    </div>
  </TitleCard>
</template>

<script setup lang="ts">
import { ProxyOrderStatusEnum } from "@/enums";
import Qgwc from "@/static/qgwc.png";
import Qgz from "@/static/qgz.png";
import Wdg from "@/static/wdg.png";
import Wks from "@/static/wks.png";
import Yrk from "@/static/yrk.png";

const { openWebview } = useNavigate({
  webview: { withToken: true, autoOrigin: true },
});
const KEY_MINE_PROXY = "mineProxy";

const entity = ref([
  {
    name: t("wei-kai-shi"),
    icon: Wks,
    url: "/proxy-h5/pages/order/index?orderStatus=A00",
    status: ProxyOrderStatusEnum.A00,
    badge: 0,
  },
  {
    name: t("wei-dao-gang"),
    icon: Wdg,
    url: "/proxy-h5/pages/order/index?orderStatus=B00",
    status: ProxyOrderStatusEnum.B00,
    badge: 0,
  },
  {
    name: t("qing-guan-zhong"),
    icon: Qgz,
    url: "/proxy-h5/pages/order/index?orderStatus=C10",
    status: ProxyOrderStatusEnum.C10,
    badge: 0,
  },
  {
    name: t("qing-guan-wan-cheng"),
    icon: Qgwc,
    url: "/proxy-h5/pages/order/index?orderStatus=C20",
    status: ProxyOrderStatusEnum.C20,
    badge: 0,
  },
  {
    name: t("yi-ru-ku"),
    icon: Yrk,
    url: "/proxy-h5/pages/order/index?orderStatus=C30",
    status: ProxyOrderStatusEnum.C30,
    badge: 0,
  },
]);
const { activeMerchant } = storeToRefs(useUserStore());

watch(activeMerchant, () => {
  getOrderCount();
});
function getOrderCount() {
  const apiList = entity.value.map(item => {
    return getOrderCountByStatus(item.status).then(res => {
      item.badge = res.data;
      return item;
    });
  });
  Promise.allSettled(apiList)
    .then(() => {
      uni.setStorageSync(KEY_MINE_PROXY, entity.value);
    })
    .catch(err => {
      console.error(err);
    });
}

async function getOrderCountByStatus(status: ProxyOrderStatusEnum) {
  return await agentCustomerApi.ApiOrderGetordertotalGet({
    orderStatus: status,
  });
}

function tryInitStroage() {
  const storageData = uni.getStorageSync(KEY_MINE_PROXY);
  if (storageData) {
    try {
      entity.value = JSON.parse(storageData);
    } catch {}
  }

  return [];
}

onBeforeMount(() => {
  tryInitStroage();
  getOrderCount();
});
</script>
