import type { MatchTypes } from "@/api";
import { TradeProductType, TradeSaleType } from "@/enums";
import { formatDate, formatDateTimeyMdhm, isToday } from "@/utils/date";
import { isNil } from "lodash";

// 获取显示的地址
function getAddress(data: MatchTypes.ProductAppRsp): string {
  let address = "";
  if (data.productType?.code === TradeProductType.supply) {
    address =
      data?.saleType?.code === TradeSaleType.spot
        ? data.deliveryCity ?? ""
        : data.destinatePortName ?? "";
  } else if (data.productType?.code === TradeProductType.purchase) {
    address = data?.receiveCity ?? "";
  }

  return address;
}

export function useTradeProduct(data: MatchTypes.ProductAppRsp) {
  const { isLogined } = useUserStore();
  const { tags } = useTagList(data);

  const styledText =
    data.productType?.code === TradeProductType.purchase
      ? {
          color: unotheme.colors.trade.purchase,
          text: t("qiu-gou"),
          emptyPriceText: t("xie-shang-xun-jia"),
          matchText: t("tiao") + t("gong-ying"),
        }
      : {
          color: unotheme.colors.trade.supply,
          text: t("gong-ying"),
          emptyPriceText: t("xie-shang-bao-jia"),
          matchText: t("tiao") + t("qiu-gou"),
        };
  const showImage = ref(data.productType?.code === TradeProductType.supply);
  const showNegotiableLabel = ref(
    data.productType?.code === TradeProductType.purchase &&
      !isNil(data.price) &&
      isLogined,
  );

  const publishTime = isToday(data.publishTime)
    ? formatDateTimeyMdhm(data.publishTime)
    : formatDate(data.publishTime);
  const address = getAddress(data);
  const saleAddress = [data.saleType?.desc, address]
    .filter(str => str)
    .join(" | ");

  let priceText = t("deng-lu-ke-jian-jia-ge");
  let unitText = "";

  if (isLogined) {
    priceText = isNil(data.price)
      ? styledText.emptyPriceText
      : data.price.toString();
    unitText =
      !isNil(data.price) && data.unitPricePer
        ? `${data.priceCurrency ?? ""}/${data.unitPricePer}`
        : "";
  }

  function toDetail() {
    if (data.productType?.code === TradeProductType.supply) {
      uni.navigateTo({
        url: "/subpages/trade/supply".toParamsUrl({
          id: data.id,
        }),
      });
    } else {
      uni.navigateTo({
        url: "/subpages/trade/purchase".toParamsUrl({
          id: data.id,
        }),
      });
    }
  }

  return {
    styledText,
    publishTime,
    saleAddress,
    priceText,
    unitText,
    tags,
    showImage,
    showNegotiableLabel,
    toDetail,
  };
}
