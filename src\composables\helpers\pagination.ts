export class Pagination {
  private _total: number = 0;
  private _pageSize: number = 10;
  private _currentPage: number = 1;

  public onPageChange: (() => void) | null = null;

  constructor(_?: { total?: number, page?: number, pageSize?: number }) {
    this._total = _?.total ?? 0;
    this._pageSize = _?.pageSize ?? 10;
    this._currentPage = _?.page ?? 1;
  }

  get pageSize(): number {
    return this._pageSize;
  }

  get currentPage(): number {
    return this._currentPage;
  }

  get isFirstPage(): boolean {
    return this._currentPage === 1;
  }

  get isLastPage(): boolean {
    return this.currentPage === this.totalPages(this._total, this._pageSize);
  }

  public setTotal(total: number): void {
    this._total = total;
  }

  public setPage(page: number): void {
    this._currentPage = page;
  }

  public setPageSize(pageSize: number): void {
    this._pageSize = pageSize;
  }

  public setCurrentPage(currentPage: number): void {
    this._currentPage = currentPage;
  }

  public next(): void {
    this._currentPage++;
  }

  public prev(): void {
    this._currentPage--;
  }

  private totalPages(total: number, pageSize: number): number {
    return Math.ceil(total / pageSize);
  }
}
