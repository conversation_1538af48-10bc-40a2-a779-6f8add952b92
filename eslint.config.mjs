import uniHelper from "@uni-helper/eslint-config";
import validateFilename from "eslint-plugin-validate-filename";

export default uniHelper({
  uni: true,
  uniJson: true,
  unocss: true,
  plugins: {
    "validate-filename": validateFilename,
  },
  vue: {
    overrides: {
      "vue/custom-event-name-casing": ["kebab-case" | "camelCase"],
      "vue/singleline-html-element-content-newline": "off",
      "vue/multiline-html-element-content-newline": "off",
      "vue/block-order": ["warn", { order: ["template", "script", "style"] }],
      "vue/max-attributes-per-line": [
        2,
        {
          multiline: 1,
          singleline: 3,
        },
      ],
    },
  },
  rules: {
    "vue/singleline-html-element-content-newline": "off",
    "vue/multiline-html-element-content-newline": "error",
    "vue/operator-linebreak": "off",
    "ts/ban-ts-comment": "off",
    "style/array-bracket-spacing": "warn",
    "unused-imports/no-unused-vars": "warn",
    "no-console": "off",
    "style/quotes": ["warn", "double"],
    "style/semi": ["warn", "always"],
    "style/no-trailing-spaces": "warn",
    "style/comma-dangle": ["warn", "always-multiline"],
    "style/no-multiple-empty-lines": "warn",
    "style/indent": "off",
    "vue/html-indent": "off",
    "no-extend-native": "off",
    "style/key-spacing": "off",
    "style/eol-last": "warn",
    "style/member-delimiter-style": "off",
    "style/indent-binary-ops": "off",
    "style/brace-style": "off",
    "style/arrow-parens": "off",
    "style/quote-props": "off",
    "style/operator-linebreak": "off",
    "regexp/no-super-linear-backtracking": "off",
    "vue/max-attributes-per-line": "off",
    "antfu/consistent-list-newline": "off",
    "antfu/if-newline": "off",
    "validate-filename/naming-rules": [
      "error",
      {
        rules: [
          {
            target: "**/components/**/**.vue",
            case: "pascal",
          },
          {
            target: "**/pages/*/*.vue",
            case: "camel",
          },
          {
            target: "**/subpages/*/*.vue",
            case: "camel",
          },
        ],
      },
    ],
  },
  ignores: ["src/wxcomponents/*", "src/subpages/components/MpHtml2/*"],
});
