<template>
  <view class="overflow-hidden rounded-13rpx">
    <view class="tabs gap-x-30rpx">
      <view class="w-140rpx tab text-left!">柜号</view>
      <view class="w-70rpx tab">ETA</view>
      <view class="flex-1 tab">目的港</view>
      <view class="w-80rpx tab">中转日期</view>
      <view class="flex-1 tab">中转港</view>
      <view class="w-80rpx tab text-right!">状态</view>
    </view>
    <view
      v-for="(item, index) in data"
      :id="`zp-id-${index}`"
      :key="index"
      class="box"
      :class="{ white: index % 2 === 0 }"
      @click="emit('click', item)"
    >
      <view class="info gap-x-30rpx">
        <view class="title w-140rpx">{{ item.cabinetNo }}</view>
        <view class="content w-70rpx">
          {{ handleItemDate(item, "finalVoyageEta") }}
        </view>
        <view class="content flex-1">{{ handleItem(item, "portName") }}</view>
        <view class="content w-80rpx">
          {{ handleItemDate(item, "transferHarbourDate") }}
        </view>
        <view class="content flex-1">
          {{ handleItem(item, "transitHarbourName") }}
        </view>
        <view class="content w-80rpx text-right!">即将到岗</view>
      </view>
      <view class="goods gap-x-30rpx">
        <view class="title">{{ item.contract }}</view>
        <view class="name">{{ goodsName(item) }}</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import type { AgentCustomerTypes } from "@/api";
import dayjs from "dayjs";

type IOrderVoKey = keyof AgentCustomerTypes.IOrderVo;

withDefaults(defineProps<{ data: AgentCustomerTypes.IOrderVo[] }>(), {
  data: () => [],
});

const emit = defineEmits(["click"]);

function handleItemDate(item: AgentCustomerTypes.IOrderVo, key: IOrderVoKey) {
  return item[key] ? dayjs(item[key] as string).format("MM-DD") : "-";
}

function handleItem(item: AgentCustomerTypes.IOrderVo, key: IOrderVoKey) {
  return item[key] ? (item[key] as string).split("（")[0] : "-";
}

function goodsName(item: AgentCustomerTypes.IOrderVo) {
  const goods: AgentCustomerTypes.IOrderDetailVo[] =
    item?.orderDetailList || [];

  const country: string[] = [];
  const factory: string[] = [];
  const goodsName: string[] = [];

  goods.forEach(i => {
    if (i.countryName) {
      country.push(i.countryName);
    }

    if (i.factoryCode) {
      factory.push(i.factoryCode);
    }

    if (i.spName || i.goodsName) {
      goodsName.push(i.spName || i.goodsName || "");
    }
  });

  return `${[...new Set(country)].join(";")}|${[...new Set(factory)].join(
    ";",
  )}|${[...new Set(goodsName)].join(";")}`;
}
</script>

<style lang="scss" scoped>
.tabs {
  display: flex;
  align-items: center;
  height: 60rpx;
  padding: 0 30rpx;
  font-size: 21rpx;
  font-weight: 500;
  color: #fff;
  background-color: #606e89;

  .tab {
    // text-align: center;
  }
}

.box {
  padding: 20rpx 30rpx;
  background: #f6f8fc;

  &.white {
    background-color: #fff;
  }

  .info {
    display: flex;
    align-items: center;
    margin-bottom: 14rpx;

    .title {
      font-size: 21rpx;
      font-weight: 600;
    }

    .content {
      font-size: 21rpx;
      font-weight: 400;

      // text-align: center;
    }
  }

  .goods {
    display: flex;
    align-items: center;

    .title {
      width: auto;
      font-size: 24rpx;
      font-weight: 400;
      color: #33373f;
    }

    .name {
      flex: 1;
      overflow: hidden;
      font-size: 22rpx;
      font-weight: 600;
      color: #b8b9ba;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
