<template>
  <view class="image-container">
    <image
      v-if="!isError"
      :class="customClass"
      :mode="mode"
      :src="imageSrc"
      :style="{ width, height }"
      @error="handleError"
      @load="handleLoad"
    />
    <image
      v-if="isError"
      :class="customClass"
      :mode="mode"
      :src="defaultImage"
      :style="{ width, height }"
    />
  </view>
</template>

<script setup lang="ts">
const props = defineProps({
  url: {
    type: String,
    default: "",
  },
  mode: {
    type: String as PropType<UniHelper.ImageMode>,
    default: "aspectFit",
  },
  customClass: {
    type: String,
    default: "",
  },
  width: {
    type: String,
    default: "200rpx",
  },
  height: {
    type: String,
    default: "200rpx",
  },
  defaultImage: {
    type: String,
    default: "/youdingte_weapp/image_default.png".toThumbnailUrl(), // 替换为您的 no-data.png 地址
  },
});

const isError = ref(false);
const imageSrc = ref(props.url);

function handleError() {
  isError.value = true;
}

function handleLoad() {
  isError.value = false;
}

// Watcher
watch(
  () => props.url,
  newVal => {
    imageSrc.value = newVal;
    isError.value = false; // 重置错误状态
  },
);
</script>

<style scoped>
.image-container {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
