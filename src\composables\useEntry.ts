import { <PERSON>Header } from "@/api/constant";
import { removeQueryParam } from "@/utils/url";

interface UseEntryOption {
  immediate?: boolean;
}

// 初始化任务
export function useEntry(option: UseEntryOption = {}) {
  const { calcStatusBarHeight } = useAppStore();

  if (option?.immediate) {
    onLaunch(() => {
      calcStatusBarHeight();
      runQueryTask();
    });
  }

  // 执行查询任务
  async function runQueryTask() {
    const { isLogined, queryUserInfo, queryMerchantList, queryUnreadCount } =
      useUserStore();
    if (isLogined) {
      await queryUserInfo();
      queryMerchantList();
      queryUnreadCount();
    }
  }

  // 登录成功回调
  function loginSuccess(options?: { redirect: string; withToken?: boolean }) {
    runQueryTask();
    let redirect = options?.redirect ?? "";
    if (redirect) {
      redirect = removeQueryParam(redirect, XHeader.HToken);
      if (options?.withToken) {
        redirect = redirect.toParamsUrl({
          withToken: true,
        });
      }
      uni.redirectTo({
        url: redirect,
      });
    } else {
      useNavigate().redirectToIndex();
    }
  }

  return {
    runQueryTask,
    loginSuccess,
  };
}
