<template>
  <div class="rounded-card px-20rpx py-24rpx">
    <ProductTitle :styled-text="product.styledText">
      {{ rspData?.productName }}
    </ProductTitle>
    <div class="mt-20rpx flex flex-col gap-16rpx">
      <ProductAttribute :title="$t('jia-ge')">
        <ProductPrice
          :price="product.priceText"
          :unit="product.unitText"
          :show-negotiable="product.showNegotiableLabel.value"
        />
      </ProductAttribute>
      <ProductAttribute
        v-if="rspData?.qty && rspData?.qtyUnit"
        :title="$t('shu-liang')"
      >
        <ProductUnit :qty="rspData?.qty" :qty-unit="rspData?.qtyUnit" />
      </ProductAttribute>
      <ProductAttribute :title="$t('fa-bu-ren')">
        {{ rspData?.publishUserName }}
      </ProductAttribute>
      <ProductAttribute :title="$t('fa-bu-shi-jian')">
        {{ product?.publishTime }}
      </ProductAttribute>
    </div>
    <MatchesLabel
      v-if="rspData.numberOfMatches"
      :number-of-matches="rspData.numberOfMatches"
      :match-text="product.styledText.matchText"
    />
  </div>
</template>

<script setup lang="ts">
import type { MatchTypes } from "@/api";
import MatchesLabel from "@/components/Trade/MatchesLabel.vue";
import ProductAttribute from "@/components/Trade/ProductAttribute.vue";
import ProductPrice from "@/components/Trade/ProductPrice.vue";
import ProductTitle from "@/components/Trade/ProductTitle.vue";
import ProductUnit from "@/components/Trade/ProductUnit.vue";

defineProps<{
  rspData: MatchTypes.PurchaseProductAppRsp | MatchTypes.SupplyProductAppRsp;
  product: ReturnType<typeof useTradeProduct>;
}>();
</script>
