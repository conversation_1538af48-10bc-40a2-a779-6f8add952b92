<template>
  <div class="px-[14rpx] text-26rpx" @click="toDetail">
    <nut-row type="flex" align="center">
      <image src="@/static/news_icon.png" class="size-36rpx" />
      <div class="h-36rpx flex-1 text-ellipsis">
        {{ data?.contentTitle ?? "" }}
      </div>
    </nut-row>
  </div>
</template>

<script setup lang="ts">
import type { QueryBackendTypes } from "@/api";

const props = defineProps<{ data: QueryBackendTypes.IArticleVo | null }>();
const { openWebview } = useNavigate();
function toDetail() {
  if (props.data) {
    openWebview({
      url: `/youdingte-news-h5/news-detail/${props.data.id}`,
      autoOrigin: true,
    });
  }
}
</script>
