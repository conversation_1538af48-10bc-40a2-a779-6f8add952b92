import { Config } from "@/config";

interface IUploadTokenVo {
  fileHost?: string;
  uploadToken?: string;
}

type Channel = "ocr" | "app";

export function useUpload(channel: Channel) {
  async function fetchToken(): Promise<IUploadTokenVo | null> {
    if (channel === "ocr") {
      return (await ocrApi.ApiCloudfileUploadTokenGet()).data;
    } else if (channel === "app") {
      return (await platformApi.ApiCloudFileUploadTokenGet()).data;
    }

    return null;
  }

  async function uploadSingleImage(filePath: string): Promise<string> {
    const uploadPayload = await fetchToken();
    return new Promise((resolve, reject) => {
      uni.showLoading();
      uni.uploadFile({
        url: Config.qiniuUploadUrl,
        fileType: "image",
        filePath,
        name: "file",
        formData: {
          token: uploadPayload?.uploadToken,
        },
        success: ({ data }) => {
          const url = `${uploadPayload?.fileHost ?? ""}/${
            JSON.parse(data).key
          }`;
          resolve(url);
        },
        fail: err => {
          showMsg(err.errMsg);
          reject(err);
        },
        complete: () => {
          uni.hideLoading();
        },
      });
    });
  }

  function chooseImage(): Promise<string> {
    return new Promise((resolve, reject) => {
      uni.chooseImage({
        count: 1,
        success: ({ tempFilePaths }) => {
          resolve(tempFilePaths[0]);
        },
        fail: err => {
          reject(err);
        },
      });
    });
  }

  return {
    chooseImage,
    uploadSingleImage,
  };
}
