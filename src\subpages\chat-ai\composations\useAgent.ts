import type {
  ContentBlock,
  IActionProcess,
  IActionProcessItem,
  IAgentMessage,
  IWorkflowNode,
} from "@/api/servers/chat/type";
import type { ParsedData, ParsedDataData } from "./useTransform";
import {
  ContentBlockTypeEnum,
  EventEnum,
  IActionProcessStatus,
  IWorkflowNodeStatus,
  MessageStatusEnum,
  WorkflowsStatus,
} from "@/enums/chat";
import { isEmpty } from "lodash";
import { useTransform } from "./useTransform";

export function useAgent() {
  const result = "";
  const contentBlocks: ContentBlock[] = [];
  // 处理工作流节点更新
  const handleWorkflowNodeUpdate = (lastItem: ContentBlock, innerData: any) => {
    const newNode: IWorkflowNode = {
      id: innerData.id,
      status: IWorkflowNodeStatus.Running,
      type: innerData.node_type,
      title: innerData.title,
      action_type: innerData.action_type,
      is_card: innerData.is_card,
      action_process: {
        action_process: [] as IActionProcessItem[],
        action_type: innerData.action_type,
      } as IActionProcess,
    };

    lastItem.workflows.nodes.push(newNode);
  };

  // 处理工作流节点工作状态
  const handleWorkflowNodeWorking = (
    lastItem: ContentBlock,
    innerData: ParsedDataData,
  ) => {
    lastItem.workflows.nodes = lastItem.workflows.nodes.map(
      (item: IWorkflowNode, index: number) => {
        if (index !== lastItem.workflows.nodes.length - 1) return item;

        const processNode = (node: IWorkflowNode): IWorkflowNode => {
          const outputs = node.outputs || "";
          const actionProcess = node.action_process as IActionProcess;
          const curIndex = Array.isArray(actionProcess?.action_process)
            ? actionProcess.action_process.length - 1
            : -1;
          const curItem =
            curIndex >= 0
              ? (actionProcess.action_process[curIndex] as IActionProcessItem)
              : null;

          const newActionProcess = {
            action_process: Array.isArray(actionProcess?.action_process)
              ? [...actionProcess.action_process]
              : ([] as IActionProcessItem[]),
            action_type: actionProcess.action_type,
          };

          if (innerData.action_type === IActionProcessStatus.tools) {
            if (curItem && curItem.id === innerData.action_process?.id) {
              const updatedItem: IActionProcessItem = {
                id: curItem.id,
                content: `${curItem.content || ""}${
                  innerData.action_process?.content || ""
                }`,
                status: innerData.action_process?.status,
                title: innerData.action_process?.title,
                description: innerData.action_process?.description,
                search_content: innerData.action_process?.search_content
                  ? [
                      ...(curItem.search_content || []),
                      ...innerData.action_process.search_content,
                    ]
                  : curItem.search_content,
              };
              newActionProcess.action_process[curIndex] = updatedItem;
            } else {
              newActionProcess.action_process.push(
                innerData.action_process as IActionProcessItem,
              );
            }
          } else if (innerData.action_type === IActionProcessStatus.llm) {
            // 对于 llm 类型，我们将 action_process 转换为字符串

            newActionProcess.action_process = `${
              actionProcess.action_process || ""
            }${innerData.action_process || ""}` as any;
          }
          if (innerData.action_type) {
            newActionProcess.action_type = innerData.action_type;
          }

          return {
            ...node,
            outputs: outputs + (innerData.outputs || ""),
            status: IWorkflowNodeStatus.Success,
            inputs: innerData.inputs || node.inputs,
            process_data: innerData.process_data,
            is_card: innerData.is_card,
            elapsed_time: innerData.elapsed_time,
            execution_metadata: innerData.execution_metadata,
            action_process: newActionProcess,
          };
        };

        return processNode(item);
      },
    );
  };

  // 处理工作流节点完成状态
  const handleWorkflowNodeFinished = (
    lastItem: ContentBlock,
    innerData: ParsedDataData,
  ) => {
    lastItem.workflows.nodes = lastItem.workflows.nodes.map(
      (item: IWorkflowNode, index: number) => {
        if (index !== lastItem.workflows.nodes.length - 1) return item;
        console.log("handleWorkflowNodeFinished", innerData.outputs);

        const processNode = (nodeItem: IWorkflowNode) => {
          let actionContent = "";
          let cardList = [];
          if (innerData.outputs?.is_card) {
            cardList = innerData.outputs.action_content as any[];
          } else {
            actionContent =
              (nodeItem.outputs?.action_content || "") +
              innerData.outputs?.action_content;
          }
          return {
            ...nodeItem,
            status: IWorkflowNodeStatus.Success,
            inputs: innerData.inputs,
            actionContent,
            cardList,
            process_data: innerData.process_data,
            elapsed_time: innerData.elapsed_time,
            is_card: innerData.outputs.is_card,
            card_type: innerData.outputs.card_type,
            ai_search_key: innerData.outputs.ai_search_key,
            execution_metadata: innerData.execution_metadata,
          };
        };

        return processNode(item);
      },
    );
  };

  // 处理不用类型chunk数据
  const handleStreamEvents = async (
    parsedData: ParsedData,
    context: {
      result: string;
      contentBlocks: ContentBlock[];
      onSuccess: (data: IAgentMessage) => void;
      onUpdate: (data: IAgentMessage) => void;
      onError: (error: { name: string; message: string }) => void;
    },
  ) => {
    const { result, contentBlocks, onSuccess, onUpdate, onError } = context;
    const lastItem = contentBlocks[contentBlocks.length - 1];

    try {
      switch (parsedData.event) {
        case EventEnum.THINK: {
          const think = parsedData.answer || "";
          if (lastItem?.type === ContentBlockTypeEnum.think) {
            lastItem.content += think;
          } else {
            contentBlocks.push({
              type: ContentBlockTypeEnum.think,
              content: think,
              isThink: false,
              workflows: {
                status: WorkflowsStatus.Running,
                nodes: [],
              },
            });
          }
          onUpdate({
            content: result,
            contentBlocks,
          });
          break;
        }
        case EventEnum.THINK_FINISHED: {
          if (lastItem?.type === ContentBlockTypeEnum.think) {
            lastItem.isThink = true;
            onUpdate({
              content: result,
              contentBlocks,
            });
          }
          break;
        }
        case EventEnum.MESSAGE_END: {
          onSuccess({
            content: result,
            message_id: parsedData.intention_id,
            contentBlocks,
          });
          break;
        }

        case EventEnum.WORKFLOW_STARTED: {
          console.log("工作流开始", parsedData);
          contentBlocks.push({
            type: ContentBlockTypeEnum.workflow,
            content: result,
            workflows: {
              status: WorkflowsStatus.Running,
              nodes: [],
            },
          });
          onUpdate({
            content: result,
            contentBlocks,
          });
          break;
        }

        case EventEnum.WORKFLOW_NODE_STARTED: {
          if (lastItem) {
            handleWorkflowNodeUpdate(lastItem, parsedData.data);
            onUpdate({
              content: result,
              contentBlocks,
            });
          }
          break;
        }

        case EventEnum.WORKFLOW_NODE_WORKING: {
          if (lastItem) {
            handleWorkflowNodeWorking(lastItem, parsedData.data);
            onUpdate({
              content: result,
              contentBlocks,
            });
          }
          break;
        }

        case EventEnum.WORKFLOW_NODE_FINISHED: {
          if (lastItem) {
            handleWorkflowNodeFinished(lastItem, parsedData.data);
            onUpdate({
              content: result,
              contentBlocks,
            });
          }
          break;
        }
        case EventEnum.WORKFLOW_FINISHED: {
          console.log("工作流结束", parsedData, lastItem);
          lastItem.workflows.status = WorkflowsStatus.Finished;

          onUpdate({
            content: result,
            contentBlocks,
          });
          break;
        }

        case EventEnum.ERROR: {
          onError({
            name: "StreamError",
            message: "流处理过程中发生错误",
          });
          break;
        }

        case EventEnum.MESSAGE: {
          const text = parsedData.answer || "";
          const newResult = result + text;

          if (lastItem?.type === ContentBlockTypeEnum.content) {
            lastItem.content += text;
          } else {
            contentBlocks.push({
              type: ContentBlockTypeEnum.content,
              content: text,
              workflows: {
                status: WorkflowsStatus.Running,
                nodes: [],
              },
            });
          }
          onUpdate({
            content: newResult,
            contentBlocks,
          });
          break;
        }
      }
    } catch (error) {
      console.error("处理流事件错误:", error);
      onError({
        name: "StreamEventError",
        message: "处理流事件时发生错误",
      });
    }
  };

  const agent = async (params: any, handlers: any) => {
    const { onSuccess, onError, onUpdate, assistantMessageId } = handlers;

    try {
      const { data: parsedData, status } = params;

      if (status === "done") {
        onSuccess({ content: result, contentBlocks });
        return;
      }

      if (!parsedData || isEmpty(parsedData)) return;
      const { streamTransform } = useTransform();
      const transformData = streamTransform(parsedData);
      // 处理流事件，每次有新内容时调用 updateMessage
      await handleStreamEvents(transformData, {
        result,
        contentBlocks,
        onSuccess,
        onUpdate: data => {
          onUpdate(assistantMessageId, {
            content: data.content,
            contentBlocks: data.contentBlocks,
            status:
              status === "done"
                ? MessageStatusEnum.Finish
                : MessageStatusEnum.Loading,
          });
        },
        onError,
      });
    } catch (error) {
      console.error("agent error", error);

      onError &&
        onError({
          name: "AgentError",
          message:
            error instanceof Error ? error.message : "agent处理流数据异常",
        });
    }
  };

  return { agent };
}
