import type { FormItemRule } from "nutui-uniapp";
import { isEmailStr, isPhoneStr } from "oig-utils";

const DEFAULT_LABEL = t("gai-zi-duan");

export function required(label = DEFAULT_LABEL): FormItemRule {
  return {
    required: true,
    message: t("label-bu-neng-wei-kong", [label]),
  };
}

export function isPhone(): FormItemRule {
  return {
    validator: value => isPhoneStr(value),
    message: t("shou-ji-ge-shi-bu-zheng-que"),
  };
}

export function isEmail(): FormItemRule {
  return {
    validator: value => isEmailStr(value),
    message: t("you-xiang-ge-shi-bu-zheng-que"),
  };
}

export function lengthLessThan(max: number): FormItemRule {
  return {
    validator: value => value.length <= max,
    message: t("chang-du-bu-neng-da-yu-max", [max]),
  };
}

export function lengthGreaterThan(min: number): FormItemRule {
  return {
    validator: value => value.length >= min,
    message: t("chang-du-bu-neng-xiao-yu-min", [min]),
  };
}

export function lengthBetween(min: number, max: number): FormItemRule {
  return {
    validator: value => value.length >= min && value.length <= max,
    message: t("chang-du-yao-zai-min-dao-max-ge-zi-fu", [min, max]),
  };
}

export function passwordLength(): FormItemRule {
  return lengthBetween(8, 16);
}
