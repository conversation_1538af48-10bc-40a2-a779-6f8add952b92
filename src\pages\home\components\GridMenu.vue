<template>
  <div v-if="groupGridApps.length > 0" class="grid-menu-card">
    <scroll-view
      scroll-x
      class="h-288rpx whitespace-nowrap"
      enhanced
      scroll-with-animation
      @scroll="scrollHandle"
    >
      <div
        v-for="(group, index) in groupGridApps"
        :key="index"
        class="gridApps inline-block h-100% w-100%"
        :style="{
          maxWidth:
            groupGridApps.length > 1
              ? `${Math.ceil(group.length / 2) * averageWidth}px`
              : '100%',
        }"
      >
        <div class="h-100% flex flex-wrap">
          <div
            v-for="item in group"
            :key="item.appId"
            :style="{ width: `${averageWidth}px` }"
            @click="() => handleGridAppNavigate(item)"
          >
            <div class="relative h-full w-full text-center">
              <slot :item="item" />
            </div>
          </div>
        </div>
      </div>
    </scroll-view>
    <div class="mt-20rpx w-100% f-c-c">
      <div
        v-for="(item, i) in groupGridApps"
        :key="i"
        class="mr-4rpx h-4rpx w-12rpx b-rd-2rpx bg-#d8d8d8"
        :class="activeIndex === i ? activeClass : ''"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import type { PlatformTypes } from "@/api";
import type { ScrollViewOnScrollEvent } from "@uni-helper/uni-app-types";
import { getAllRect } from "@/utils/node";

interface IAppFunctionVo extends PlatformTypes.IAppFunctionByTypeVo {
  badge?: number;
}

const props = defineProps<{
  gridAppList: IAppFunctionVo[];
}>();

const cols = 5;
const rows = 2;
const size = cols * rows;
const activeIndex = ref(0);
const activeClass = "w-20rpx bg-#0091DB!";
const { handleGridAppNavigate } = useNavigate();
const instance =
  getCurrentInstance() as unknown as WechatMiniprogram.Component.TrivialInstance;
const groupElement = ref();
const averageWidth = ref();

watch(
  () => props.gridAppList,
  async () => {
    groupElement.value = await getAllRect(instance, ".gridApps");
    const maxWidth = groupElement.value?.[0]?.width;
    averageWidth.value = maxWidth / cols;
  },
);

const groupGridApps = computed(() => {
  const _apps = Array.from(
    { length: Math.ceil(props.gridAppList.length / size) },
    (_, i) => {
      let list = props.gridAppList.slice(i * size, i * size + size);

      if (i !== 0) {
        list = [
          ...list.filter((_, i) => i % 2 === 0),
          ...list.filter((_, i) => i % 2 !== 0),
        ];
      }
      return list;
    },
  );

  return _apps;
});

// 滚动方向
const direction = ref<"left" | "right">();

function scrollHandle(event: ScrollViewOnScrollEvent) {
  const { scrollLeft, deltaX } = event.detail;
  const groupEle = groupElement.value.filter((item: any, i: number) => i > 0);
  direction.value = deltaX > 0 ? "left" : "right";

  groupEle.forEach((el: any, i: number) => {
    if (scrollLeft > Math.ceil(el.width / 2)) {
      activeIndex.value = i + 1;
    } else {
      activeIndex.value = 0;
    }
  });
}

// groupGridApps 第二屏列数
// const groupGridAppsSecondCols = computed(() => {
//   if (unref(groupGridApps)?.[1]?.length > 0) {
//     return Math.ceil(unref(groupGridApps)[1].length / 2);
//   } else {
//     return 0;
//   }
// });
// const groupGridAppsSecondColWidths = computed(() => {
//   const _widths = [];
//   for (let index = 0; index < unref(groupGridAppsSecondCols); index++) {
//     const width = (index + 1) * unref(averageWidth);
//     _widths.push(width);
//   }
//   return _widths;
// });

// const _scrollLeft = ref(0);
// function binddragend(event: any) {
//   const { scrollLeft, velocity } = event?.detail ?? {};

// const multi =
//   unref(direction) === "right"
//     ? Math.ceil(scrollLeft / averageWidth.value)
//     : Math.floor(scrollLeft / averageWidth.value);
// const target = multi * averageWidth.value;
// console.log(target, scrollLeft - target);
// if (Math.abs(scrollLeft - target) > 10) {
//   _scrollLeft.value = target;
// }

// scrollLeft 位于 groupGridAppsSecondColWidths 的哪个区间
//   const index = unref(groupGridAppsSecondColWidths).findIndex(
//     width => scrollLeft < width,
//   );
//   if (index === -1 || scrollLeft < 0) {
//     return;
//   }

//   let _nextScrollLeft = 0;
//   if (unref(direction) === "right") {
//     _nextScrollLeft = unref(groupGridAppsSecondColWidths)[index];
//   } else {
//     if (index === 0) {
//       _nextScrollLeft = 0;
//     } else {
//       _nextScrollLeft = unref(groupGridAppsSecondColWidths)[index - 1];
//     }
//   }

//   _scrollLeft.value = _nextScrollLeft;
// }
</script>

<style scoped></style>
