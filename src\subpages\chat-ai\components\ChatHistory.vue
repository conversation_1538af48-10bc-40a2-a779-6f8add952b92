<template>
  <nut-popup
    v-model:visible="show"
    position="left"
    round
    :z-index="9"
    :custom-style="{
      width: state.width,
      height: state.height,
      top: state.top,
    }"
    :safe-area-inset-bottom="true"
    :safe-area-inset-top="true"
  >
    <div class="box-border h-full flex flex-col bg-#F4F5F7 pt-34rpx">
      <div class="mb-8rpx px-30rpx text-32rpx c-#153147 font-500 lh-44rpx">
        历史记录
      </div>
      <div class="flex-1 overflow-hidden">
        <nut-list :height="40" :list-data="historyList" class="nut-list">
          <template #default="{ item }">
            <div
              v-if="item.isGroup"
              class="mb-15rpx mt-46rpx h-34rpx px-30rpx text-24rpx c-#999999 font-400 lh-34rpx"
            >
              {{ item.group }}
            </div>
            <div
              v-else
              class="mx-18rpx h-42rpx text-ellipsis px-12rpx py-18rpx text-30rpx font-400 lh-42rpx"
              :class="[
                sessionId === item.session_id
                  ? 'font-600  bg-#DDF0FF rounded-8rpx c-#008FD6'
                  : ' font-400 c-#33373F',
              ]"
              @click="handleSession(item)"
            >
              {{ item.session_name }}
            </div>
          </template>
        </nut-list>
      </div>
      <nut-safe-area position="bottom" />
    </div>
  </nut-popup>
</template>

<script setup lang="ts">
import type { ChatTypes } from "@/api";
import { useChatStore } from "@/store/modules/chat";
import { groupByKey } from "@/utils/shared";
import { useSession } from "../composations/useSession";

defineOptions({
  options: { styleIsolation: "shared" },
});

const show = defineModel<boolean>({ required: true });

const { customBarHeight } = storeToRefs(useAppStore());
const state = reactive({
  width: "70%",
  height: `calc(100% - ${customBarHeight.value}px)`,
  top: `${customBarHeight.value}px`,
});

const { querySessionMessages } = useSession();
const chatStore = useChatStore();
const { setSessionId } = chatStore;
const { conversations, loading, sessionId } = storeToRefs(chatStore);

const historyList = computed(() => {
  return groupByKey<ChatTypes.Sessions>(conversations.value, "group");
});

async function handleSession(item: ChatTypes.Message) {
  try {
    setSessionId(item.session_id);
    loading.value.pageLoading = true;
    await querySessionMessages(item.session_id);
    show.value = false;
  } catch (err) {
    console.log(err);
  } finally {
    loading.value.pageLoading = false;
  }
}
</script>

<style lang="scss" scoped>
::v-deep .nut-list {
  height: 100% !important;
}
</style>
