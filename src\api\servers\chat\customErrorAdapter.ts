import type { AxiosError } from "axios";
import { ResponseCode } from "@/api/constant";

const INVALID_SESSION = 451; // 451错误码则说明sessionId已过期, 将重新获取sessionId

export async function customErrorAdapter(error: AxiosError) {
  const { logout } = useUserStore();
  const LOGOUT_STATUS = [ResponseCode.unauthorized];
  const { config, response } = error;
  const { data, status } = response ?? { data: null, status: null };
  if (config?.showLoading) {
    uni.hideLoading();
  }

  if (status) {
    if (LOGOUT_STATUS.includes(status)) {
      logout();
      return Promise.reject(error);
    } else if (status === INVALID_SESSION) {
      throw Promise.reject(error);
    }
  } else if (config?.showError) {
    showMsg(data?.msg || "unknown error", {
      duration: 3000,
    });
  }
  console.log("customErrorAdapter", status);

  return Promise.reject(error);
}
