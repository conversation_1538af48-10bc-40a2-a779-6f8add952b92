<template>
  <div class="h-70rpx f-c bg-white pl-40rpx">
    <div
      v-for="tab in tabs"
      :key="tab.value"
      :class="tab.value === value ? activeClassName : ''"
      class="relative mr-42rpx text-28rpx text-#6C6E72"
      @click="onClick(tab)"
    >
      <nut-badge :value="tab.unRead" custom-color="#FF5E5E">
        {{ tab.name }}
      </nut-badge>
      <image
        v-if="tab.value === value"
        class="absolute bottom-4rpx left-0 z-[1] h-10rpx w-full"
        src="@/static/selected_msgtab.png"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { MessageTabEnum } from "@/enums";

const emit = defineEmits<{ change: [value: MessageTabEnum] }>();
const value = defineModel<MessageTabEnum>({ required: true });
const activeClassName = "text-36rpx text-#33373F font-600 ";
const tabs = ref([
  {
    name: t("dai-li"),
    value: MessageTabEnum.UCHAIN_ROLE,
    unRead: 0,
  },
  {
    name: "金融",
    value: MessageTabEnum.FINANCE,
    unRead: 0,
    url: "/bop-scfp-h5/cockpit/message",
  },
  {
    name: "官方",
    value: MessageTabEnum.OFFICIAL,
    unRead: 0,
  },
]);
const { userInfo } = storeToRefs(useUserStore());

// onBeforeMount(() => {
//   tabsUnReadCount();
// });

async function tabsUnReadCount() {
  tabs.value
    .filter(_ => !_.url)
    .forEach(async item => {
      const unReadCount = await getUnReadCount(item.value);
      item.unRead = unReadCount;
    });
}
defineExpose({
  tabsUnReadCount,
});

async function getUnReadCount(appType: MessageTabEnum) {
  const rsp = await platformApi.ApiMessageUnreadappmessagecountbyapptypePost({
    userId: userInfo.value?.userId,
    // @ts-ignore
    appType,
  });
  return rsp.data.count ?? 0;
}

const { openWebview } = useNavigate({ webview: { withToken: true } });
function onClick(tab: any) {
  if (tab.url) {
    openWebview({ url: tab.url, autoOrigin: true });
    return;
  }
  value.value = tab.value;
  tab.unRead = 0;
  emit("change", tab);
}
</script>
