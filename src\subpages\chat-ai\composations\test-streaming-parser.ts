import { parseStreamingData, resetStreamingParser } from "./useParseStreaming";

// 测试你提供的场景
function testStreamingParser() {
  console.log("=== 测试流式解析器 ===");

  // 重置解析器
  resetStreamingParser();

  // 使用你原始提供的数据格式进行测试
  const chunk1 = `data: {
  message: {
    id: "6a6222b5-5e9b-43e6-bb39-0f3920a3b247",
    session_id: "3271",
    message_id: "5236",
    intention_id: "4924",
    event_type: "node_finished",
    event_time: "2025-09-11 16:17:51",
    content: {
      action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1",
      action_content: [
        {
          orderId: "4500013082",
          cabinetNo: "MNBU4080965",
          contract: "901/2022（3101）",
          stockWeight: "19986.801",
          shelfStartExpDate: "2024/09/28",
          prodStartDate: "2022/09/29",
          prodEndDate: "2022/10/14",
          declared: "",
          finalVoyageEta: "",
          portName: "",
          transferHarbourDate: "",
          transitHarbourName: "",
          inboundNo: "ZR00003624",
          warehouseName: "深圳锋润锋冷库（同乐）",
          stockPiece: 924,`;

  const chunk2 = `   stockGrossWeight: "20584.626",
          countryName: "巴西",
          factoryCode: "SIF2146",
          goodsName: "冷冻五花肉条",
          pieceNum: 924,
          materialDesc: "冷冻去骨猪肉块",
          shelfDays: -333,
          orderStatus: "",
          shelfEndExpDate: "2024/10/13",
        },
        {
          orderId: "4500013082",
          cabinetNo: "MNBU4080965",
          contract: "901/2022（3101）",
          stockWeight: "19986.801",
          shelfStartExpDate: "2024/09/28",
          prodStartDate: "2022/09/29",
          prodEndDate: "2022/10/14",
          declared: "",
          finalVoyageEta: "",
          portName: "",
          transferHarbourDate: "",
          transitHarbourName: "",
          inboundNo: "ZR00003624",
          warehouseName: "深圳锋润锋冷库（同乐）",
          stockPiece: 924,
          stockGrossWeight: "20584.626",
          countryName: "巴西",`;

  const chunk3 = `  factoryCode: "SIF2146",
          goodsName: "冷冻五花肉条",
          pieceNum: 924,
          materialDesc: "冷冻去骨猪肉块",
          shelfDays: -333,
          orderStatus: "",
          shelfEndExpDate: "2024/10/13",
        },
      ],
      total: 300,
      ai_search_key: "",
      user_input: "查询我的库存",
      is_card: 1,
      card_type: "stockGoodsCard",
      status: "succeed",
    },
    plan_id: "3947",
    action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1",
    duration: 2.0,
  },
  event_type: "node_finished",
}

data: {
  message: {
    id: "6a6222b5-5e9b-43e6-bb39-0f3920a3b247",
    session_id: "3271",
    message_id: "5236",
    intention_id: "4924",
    event_type: "node_finished",
    event_time: "2025-09-11 16:17:51",
    content: {
      action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1",
      total: 300,
      ai_search_key: "",
      user_input: "查询我的库存",
      is_card: 1,
      card_type: "stockGoodsCard",
      status: "succeed",
    },
    plan_id: "3947",
    action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1",
    duration: 2.0,
  },
  event_type: "node_finished",
};`;

  console.log("处理 chunk1...");
  console.log("chunk1 长度:", chunk1.length);
  console.log("chunk1 预览:", `${chunk1.substring(0, 100)}...`);
  const result1 = parseStreamingData(chunk1, false);
  console.log("chunk1 解析结果:", result1.length, "个对象");

  console.log("\n处理 chunk2...");
  console.log("chunk2 长度:", chunk2.length);
  console.log("chunk2 预览:", `${chunk2.substring(0, 100)}...`);
  const result2 = parseStreamingData(chunk2, false);
  console.log("chunk2 解析结果:", result2.length, "个对象");

  console.log("\n处理 chunk3...");
  console.log("chunk3 长度:", chunk3.length);
  console.log("chunk3 预览:", `${chunk3.substring(0, 100)}...`);
  const result3 = parseStreamingData(chunk3, true);
  console.log("chunk3 解析结果:", result3.length, "个对象");

  console.log("\n=== 总结 ===");
  console.log(
    "总共解析到的对象数:",
    result1.length + result2.length + result3.length,
  );

  // 输出解析到的对象
  const allResults = [...result1, ...result2, ...result3];
  allResults.forEach((obj: any, index) => {
    console.log(`\n对象 ${index + 1}:`, {
      id: obj.message?.id,
      event_type: obj.event_type,
      action_content_length: obj.message?.content?.action_content?.length || 0,
      total: obj.message?.content?.total,
      has_action_content: !!obj.message?.content?.action_content,
    });

    // 如果有action_content，显示第一个元素的部分信息
    if (obj.message?.content?.action_content?.length > 0) {
      const firstItem = obj.message.content.action_content[0];
      console.log("  第一个action_content项:", {
        orderId: firstItem.orderId,
        goodsName: firstItem.goodsName,
        stockPiece: firstItem.stockPiece,
      });
    }
  });
}

// 导出测试函数
export { testStreamingParser };

// 直接运行测试
testStreamingParser();
