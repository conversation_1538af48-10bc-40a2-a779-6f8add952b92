import { resolve } from "node:path";
import { uniuseAutoImports } from "@uni-helper/uni-use";
import Components from "@uni-helper/vite-plugin-uni-components";
import { UniUIResolver } from "@uni-helper/vite-plugin-uni-components/resolvers";
import { NutResolver } from "nutui-uniapp";
import AutoImport from "unplugin-auto-import/vite";
import IconsResolver from "unplugin-icons/resolver";
import icons from "unplugin-icons/vite";
import VueMacros from "unplugin-vue-macros/vite";
import { getRootPath, getSrcPath } from "../utils";

export default [
  AutoImport({
    imports: ["vue", "pinia", "uni-app", "vue-i18n", uniuseAutoImports()],
    include: [/\.[tj]sx?$/, /\.vue$/],
    dirs: [
      resolve(getSrcPath(), "composables/**/*.ts"),
      resolve(getSrcPath(), "store"),
      resolve(getSrcPath(), "i18n"),
      resolve(getSrcPath(), "api/index.ts"),
      resolve(getSrcPath(), "constants/**/*.ts"),
      resolve(getSrcPath(), "styles/**/*.ts"),
    ],
    vueTemplate: true,
    dts: resolve(getRootPath(), "typings/auto-import.d.ts"),
  }),
  Components({
    resolvers: [NutResolver(), UniUIResolver(), IconsResolver()],
    extensions: ["vue"],
    deep: true,
    directoryAsNamespace: true,
    dts: resolve(getRootPath(), "typings/components.d.ts"),
  }),
  icons({
    compiler: "vue3",
  }),
  VueMacros(),
];
