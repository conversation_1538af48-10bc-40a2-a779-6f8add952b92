<template>
  <div class="relative" :style="{ height }">
    <div
      class="absolute-lb bottom-[16rpx] right-0 box-border h-68rpx w-full flex items-center border-[1rpx] border-primary rounded-[34rpx] border-solid bg-white p-[2rpx] pl-[12rpx] text-[26rpx] color-grey"
      @click="emit('click')"
    >
      <image src="@/static/search-icon.png" class="size-44rpx" />
      <div class="ml-[16rpx] flex-1 text-ellipsis">
        <template v-if="tab === HomeTabEnum.Mall">
          {{ $t("searchPlaceholder") }}
        </template>
        <swiper
          v-else

           autoplay disable-touch circular vertical
          :touchable="false"
          class="h-68rpx lh-68rpx"
        >
          <swiper-item v-for="(item, index) in list" :key="index">
            {{ item }}
          </swiper-item>
        </swiper>
      </div>
      <div
        class="h-full w-[104rpx] f-c-c rounded-[32rpx] bg-primary text-center text-[28rpx] color-white"
      >
        {{ $t("sou-suo") }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { HomeTabEnum } from "../useHomeView";

defineProps<{ height: string; tab: HomeTabEnum }>();
const emit = defineEmits(["click"]);

const list = ref([
  t("HomeSearchBar.list1"),
  t("HomeSearchBar.list2"),
  t("HomeSearchBar.list3"),
  t("HomeSearchBar.list4"),
]);
</script>
