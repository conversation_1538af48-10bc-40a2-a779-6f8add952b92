<template>
  <div class="rounded-card p-[16rpx]">
    <div class="mb-[28rpx] flex items-center">
      <div class="text-[30rpx] text-black font-600">
        {{ $t("shi-shi-hui-shuai") }}
      </div>
      <div class="ml-16rpx text-[22rpx] color-grey">
        {{ time }}
      </div>
    </div>
    <div class="flex text-[22rpx] color-black">
      <div class="flex-1">
        <div
          v-for="item in list.slice(0, 5)"
          :key="item.id"
          class="mb-[16rpx] flex items-center justify-between"
        >
          <div class="flex items-center">
            <image
              :src="`/country/${item.currency}`.toThumbnailUrl()"
              class="mr-8rpx h-48rpx w-48rpx"
            />
            <span>{{ `${item.currency}(${item.zhCurrency})` }}</span>
          </div>
          <div>{{ item.rmbRate }}</div>
        </div>
      </div>
      <div class="mx-24rpx w-2rpx bg-[#E7E7E7]" />
      <div class="flex-1">
        <div
          v-for="item in list.slice(5, 10)"
          :key="item.id"
          class="mb-[16rpx] flex items-center justify-between"
        >
          <div class="flex items-center">
            <image
              :src="`/country/${item.currency}`.toThumbnailUrl()"
              class="mr-8rpx h-48rpx w-48rpx"
            />
            <span>{{ `${item.currency}(${item.zhCurrency})` }}</span>
          </div>
          <div>{{ item.rmbRate }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { PlatformTypes } from "@/api";
import dayjs from "dayjs";

onBeforeMount(() => {
  getData();
});

const list = ref<PlatformTypes.IUchainCurrencyRateVo[]>([]);

async function getData() {
  const res = await platformApi.ApiUchainRateExchangeCurrratelistGet();
  list.value = res.data;
}

const time = computed(() => {
  const rateTime = list.value.length ? list.value[0].rateTime : "--";
  if (rateTime === "--") {
    return `${t("geng-xin-yu")}--`;
  }
  const time = dayjs(rateTime);
  const isToday = time.isSame(dayjs(), "day");
  return `${t("geng-xin-yu")}${isToday ? "今日" : ""}${
    isToday ? rateTime?.split(" ")[1] : rateTime
  }`;
});

defineExpose({
  getData,
});
</script>
