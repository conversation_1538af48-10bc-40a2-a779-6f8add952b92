<template>
  <SwitchMerchant
    v-model="merchantPopupVisible"
    @switch-success="handleSwitchMerchant"
  />
</template>

<script setup lang="ts">
import SwitchMerchant from "@/components/Merchant/SwitchMerchant.vue";
import { delay } from "oig-utils";

useTitle("切换商户");
const merchantPopupVisible = ref(true);
let redirect = "";

onLoad(options => {
  if (typeof options?.redirect === "string") {
    redirect = decodeURIComponent(options?.redirect);
  }
});

async function handleSwitchMerchant() {
  showMsg("切换商户成功", { icon: "success" });
  await delay(1000);
  if (redirect) {
    uni.navigateBack().then(async () => {
      await delay(300);
      uni.redirectTo({
        url: redirect,
      });
    });
  }
}
</script>
