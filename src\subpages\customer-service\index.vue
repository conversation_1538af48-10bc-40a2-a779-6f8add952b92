<template>
  <div class="relative h-100vh w-full flex flex-col items-center">
    <image
      class="absolute h-1244rpx w-full content-[''] -z-1"
      :src="coverImage"
    />
    <image mt-142rpx h-160rpx w-160rpx :src="avatarImage" />
    <div class="mt-74rpx text-40rpx fw-bold lh-56rpx">
      {{ $t("hi-wo-shi-you-ding-te-ke-fu-zhu-shou") }}
    </div>
    <image class="mt-20rpx h-72rpx w-460rpx" :src="labelImage" />
    <button
      class="mt-134rpx h-92rpx w-620rpx rounded-12rpx bg-#008fd6 text-30rpx c-#fff fw-500 lh-92rpx"
      open-type="contact"
      session-from="sessionFrom"
    >
      {{ $t("fa-qi-liao-tian") }}
    </button>
    <div class="mt-24rpx text-26rpx c-#8592ad lh-36rpx">
      {{ $t("ben-ci-liao-tian-bu-hui-tian-jia-dui-fang-wei-hao-you") }}
    </div>
  </div>
</template>

<script setup lang="ts">
const avatarImage = "customer-service-avatar.png".toQiniuUrl();
const labelImage = "customer-service-label.png".toQiniuUrl();
const coverImage = "customer-service-cover.png".toQiniuUrl();
</script>
