<template>
  <view
    class="mb-16rpx flex items-center justify-between rounded-12rpx bg-#fff p-18rpx last:mb-0rpx"
  >
    <view class="flex items-center">
      <image
        :src="fileIcon"
        mode="scaleToFill"
        class="mr-12rpx block h-82rpx w-82rpx"
      />
      <view class="h-40rpx text-28rpx c-#33373F font-500 lh-40rpx">
        {{ file.fileName }}
      </view>
    </view>
    <image
      :src="FileDownloadIcon"
      mode="scaleToFill"
      class="block h-50rpx w-50rpx"
    />
  </view>
</template>

<script setup lang="ts">
import FileDocIcon from "@/static/chat/file-doc-icon.png";
import FileDownloadIcon from "@/static/chat/file-download-icon.png";
import FileJpgIcon from "@/static/chat/file-jpg-icon.png";
import FileOtherIcon from "@/static/chat/file-other-icon.png";
import FilePngIcon from "@/static/chat/file-png-icon.png";
import FileTxtIcon from "@/static/chat/file-txt-icon.png";
import FileXlsIcon from "@/static/chat/file-xls-icon.png";

interface FileItemProps {
  file: {
    fileType: string; // 文件类型
    fileName: string; // 文件名
  };
}
const props = defineProps<FileItemProps>();

function getFileIcon(fileType: string) {
  switch (fileType) {
    case "xls":
      return FileXlsIcon;
    case "doc":
      return FileDocIcon;
    case "jpg":
      return FileJpgIcon;
    case "png":
      return FilePngIcon;
    case "txt":
      return FileTxtIcon;
    default:
      return FileOtherIcon;
  }
}

const fileIcon = computed(() => {
  return getFileIcon(props.file?.fileType);
});
</script>
