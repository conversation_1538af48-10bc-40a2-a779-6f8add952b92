<template>
  <div class="h-100vh bg-white">
    <SearchBar v-model="searchValue" autofocus @search="search" />
    <div class="mx-34rpx mt-50rpx">
      <div class="f-c justify-between">
        <UnderlineTitle :title="$t('sou-suo-li-shi')" />
        <image
          src="@/static/search-btn-delete.png"
          class="size-36rpx"
          mode="scaleToFill"
          @click="clear"
        />
      </div>
      <div class="f-c flex-wrap gap-20rpx py-24rpx">
        <div
          v-for="item in history.list.value"
          :key="item"
          class="history-item"
          @click="search(item)"
        >
          {{ item }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useHistory } from "@/composables/useSearchHistory";
import { delay } from "oig-utils";
import SearchBar from "./components/SearchBar.vue";
import UnderlineTitle from "./components/UnderlineTitle.vue";

useTitle(t("sou-suo"));
const searchValue = ref("");
const history = useHistory("home");

async function search(text: string) {
  uni.navigateTo({
    url: "/pages/search/index".toParamsUrl({
      value: text,
    }),
  });
  await delay(1000);
  history.add(text);
}

function clear() {
  showConfirm(t("historyClearTips")).then(history.clear);
}
</script>

<style lang="scss" scoped>
.history-item {
  @apply rounded-10rpx bg-#ECEDF1 px-30rpx py-10rpx text-28rpx color-black-trade;
}
</style>
