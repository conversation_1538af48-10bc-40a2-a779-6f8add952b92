<template>
  <nut-config-provider class="nut-config" :theme-vars="themeVars">
    <div
      v-if="loading.pageLoading"
      class="h-full flex items-center justify-center"
    >
      <NoData :loading="loading.pageLoading" />
    </div>
    <div
      v-else
      class="absolute box-border h-full w-full flex flex-col bg-#fff transition-bottom duration-300 ease-in-out"
      :style="{
        paddingTop: `${customBarHeight + 45}px`,
        bottom: `${chatBoxStyle.bottom}px`,
      }"
    >
      <CustomHeader />
      <ChatTop />
      <ChatContent class="flex-1 overflow-hidden" />
      <ChatInput @keyboardheightchange="onKeyboardheightchange" />
    </div>
  </nut-config-provider>
</template>

<script setup lang="ts">
import { useChatStore } from "@/store/modules/chat";
import ChatContent from "./components/ChatContent.vue";
import ChatInput from "./components/ChatInput.vue";
import ChatTop from "./components/ChatTop.vue";
import CustomHeader from "./components/CustomHeader.vue";
import { useSession } from "./composations/useSession";

defineOptions({
  options: { styleIsolation: "shared" },
});

const { calcStatusBarHeight } = useAppStore();
const { customBarHeight } = storeToRefs(useAppStore());
const { querySession, queryGetXyTips } = useSession();
const { loading } = storeToRefs(useChatStore());
const { logout, setToken, queryUserInfo, queryMerchantList } = useUserStore();
const { userInfo } = storeToRefs(useUserStore());

const themeVars = reactive({
  popupBorderRadius: "24rpx",
});

onLoad(async options => {
  console.log("onLoad options", options);
  calcStatusBarHeight();
  const queryToken =
    options?.[XHeader.HToken] ||
    "3770b8de69c494f41e58b6965f4093b738624d8ac149fd8fe875a2e16ef0b699";

  if (!queryToken) {
    logout();
  } else {
    setToken(queryToken);
    init();
  }
});

async function init() {
  loading.value.pageLoading = true;
  await queryUserInfo();
  queryMerchantList();
  const userId = userInfo.value?.userId;
  Promise.all([
    querySession({ user_id: userId }),
    queryGetXyTips({ user_id: userId }),
  ]).finally(() => {
    loading.value.pageLoading = false;
  });
}

const chatBoxStyle = ref({
  bottom: 0,
});
function onKeyboardheightchange(
  event: UniHelper.TextareaOnKeyboardheightchangeEvent,
) {
  chatBoxStyle.value.bottom = event.detail.height ?? 0;
}
</script>

<style lang="scss" scoped>
/* stylelint-disable */
::v-deep .nut-theme- {
  height: 100%;
  width: 100%;
  position: relative;
}
</style>
