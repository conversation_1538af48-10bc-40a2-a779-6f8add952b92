<template>
  <div class="flex-col items-center" @click="toDetailPage">
    <image
      v-show="!loadComplete"
      :src="quoteTrend.staticLargeImage"
      class="h-58rpx w-116rpx"
    />
    <image
      v-show="loadComplete"
      class="h-58rpx w-116rpx"
      :src="largeImage"
      @load="imageLoaded"
    />
    <div
      class="h-40px f-c-c text-center text-28rpx font-600 line-height-40rpx"
      :style="{ color: quoteTrend.color }"
    >
      <image :src="quoteTrend.image" class="size-36rpx" />
      {{ quoteTrend.toRatePercent() }}
    </div>
    <div class="text-24rpx text-#394253">{{ data?.targetName }}</div>
  </div>
</template>

<script setup lang="ts">
import type { NewsAppTypes } from "@/api";

const props = defineProps<{
  data: NewsAppTypes.ILargeMarketDataH5Vo;
  current?: number;
}>();
const { data } = toRefs(props);
const loadComplete = ref(false);
const { openWebview } = useNavigate();

const quoteTrend = computed(() =>
  useQuoteTrend(data.value.riseFallRate, data.value.riseFallValue),
);

const largeImage = ref("");

watch(
  () => props.current,
  () => {
    largeImage.value = `${quoteTrend.value.largeImage}?${new Date().getTime()}`;
  },
  { immediate: true },
);

function imageLoaded(event: UniHelper.ImageOnLoadEvent) {
  loadComplete.value = !!event.detail.width;
}

function toDetailPage() {
  openWebview({
    url: `/youdingte-news-h5/home?type=DAPAN&firstCode=${data.value.firstClassCode}&targetCode=${data.value.targetCode}`,
    autoOrigin: true,
  });
}
</script>

<style scoped></style>
