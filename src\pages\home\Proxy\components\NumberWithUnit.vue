<template>
  <div class="price-container" :style="{ color }">
    <span
      class="currency-symbol"
      :style="{
        fontSize: decimalFontSize,
        fontWeight: isBold ? '500' : 'normal',
      }"
    >
      {{ symbol }}
    </span>

    <span
      class="integer-part"
      :style="{
        fontSize: integerFontSize,
        fontWeight: isBold ? '600' : 'normal',
      }"
      >{{ formatNumber(value, useThousandsSeparator).integer }}</span>

    <span
      v-if="showDecimal && formatNumber(value).decimal"
      class="decimal-part"
      :style="{ fontSize: decimalFontSize }"
      >{{ formatNumber(value, useThousandsSeparator).decimal }}</span>
    <span
      v-if="suffix"
      class="suffix"
      :style="{
        fontSize: decimalFontSize,
        marginLeft: '2px',
        color: suffixColor,
      }"
      >{{ suffix }}</span>
  </div>
</template>

<script setup lang="ts">
withDefaults(
  defineProps<{
    symbol?: string;
    value: number | string;
    integerFontSize?: string;
    decimalFontSize?: string;
    useThousandsSeparator?: boolean;
    showDecimal?: boolean;
    suffix?: string;
    suffixColor?: string;
    suffixFontSize?: string;
    isBold?: boolean;
    color?: string;
  }>(),
  {
    integerFontSize: "16px",
    decimalFontSize: "12px",
    showDecimal: true,
  },
);

function formatNumber(num: number | string, useThousandsSeparator?: boolean) {
  const numberStr = typeof num === "string" ? num : num.toFixed(2);
  const [integerPart, decimalPart] = numberStr.split(".");

  return {
    integer: useThousandsSeparator
      ? integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
      : integerPart,
    decimal: decimalPart ? `.${decimalPart}` : "",
  };
}
</script>

<style scoped>
.price-container {
  display: inline-flex;
  align-items: baseline;
  font-family: Arial, sans-serif;
}

.currency-symbol {
  margin-right: 2px;
}

.integer-part {
  transition: font-size 0.3s ease;
}

.decimal-part {
  margin-left: 2px;
  transition: font-size 0.3s ease;
}
</style>
