<template>
  <GridMenuSkeleton v-if="loading && gridAppList.length === 0" :cols="cols" />
  <GridMenu :grid-app-list="gridAppList">
    <template #default="{ item }">
      <view v-if="item.badge" class="absolute right-0 top-0">
        <uni-badge :text="item.badge" type="error" />
      </view>
      <image :src="item.imageUrl" class="mt-10rpx h-[66rpx] w-[64rpx]" />
      <view
        class="mt-10rpx w-[100%] truncate text-center text-[22rpx] font-400"
      >
        {{ item.functionName }}
      </view>
    </template>
  </GridMenu>
</template>

<script setup lang="ts">
import type { PlatformTypes } from "@/api/index";
import { customApi } from "@/api/servers/custom";
import { GridAppPosition, GridAppType, YesOrNo } from "@/enums";
import GridMenu from "../../components/GridMenu.vue";

interface IAppFunctionVo extends PlatformTypes.IAppFunctionByTypeVo {
  badge?: number;
}

defineOptions({
  options: { styleIsolation: "shared" },
});

const cols = 5;
const rows = 2;
const size = cols * rows;

const KEY_HOME_PROXY_GRID = "homeProxyGrid";

const gridAppList = ref<IAppFunctionVo[]>([]);
const { isLogined, hasUchainPermission } = storeToRefs(useUserStore());

const [getData, loading] = useApiFetch(() =>
  platformApi
    .ApiAppnewFunctionsGet({
      clientType: GridAppType.miniProgram,
      showPosition: GridAppPosition.agent,
    })
    .then(async rsp => {
      gridAppList.value = rsp.data;
      const list = gridAppList.value
        .filter(_ => _.needUnreadNum === YesOrNo.yes)
        .map(async item => {
          if (isLogined.value && hasUchainPermission.value) {
            item.badge = await queryUnRead(item.unreadNumUrl!);
          }
          return item;
        });
      Promise.allSettled(list);

      uni.setStorageSync(
        KEY_HOME_PROXY_GRID,
        JSON.stringify(gridAppList.value),
      );
    }),
);

function queryUnRead(url: string) {
  return customApi.fetchUnReadNum(url).then(res => {
    return res.data;
  });
}

function tryInitStroage(): PlatformTypes.IPlatformFrontAppItemVo[] {
  const storageData = uni.getStorageSync(KEY_HOME_PROXY_GRID);
  if (storageData) {
    try {
      gridAppList.value = JSON.parse(storageData);
    } catch {}
  }

  return [];
}

onBeforeMount(() => {
  tryInitStroage();
  getData();
});

defineExpose({
  getData,
});
</script>

<style lang="scss" scoped>
::v-deep .nut-swiper-pagination {
  bottom: 0;
}
</style>
