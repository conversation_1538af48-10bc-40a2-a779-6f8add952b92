<template>
  <div
    class="mb-16rpx flex flex-col rounded-[8rpx] bg-white px-20rpx py-24rpx"
    @click="toDetail"
  >
    <div class="flex gap-[40rpx]">
      <SupplyThumbnail v-if="showImage" :data="data" />

      <div class="flex flex-col flex-grow break-after-all gap-[18rpx]">
        <ProductTitle :styled-text="styledText">
          {{ data.productName }}
        </ProductTitle>
        <div
          class="flex justify-between gap-[18rpx] text-24rpx color-grey-1"
          :class="{ 'flex-col': showImage }"
        >
          <div class="flex-1">
            {{ $t("fa-bu-shi-jian") }}：{{ publishTime }}
          </div>
          <div class="flex-1">{{ saleAddress }}</div>
        </div>

        <div class="flex justify-between text-24rpx color-grey-1">
          <div class="flex-1">
            <ProductPrice
              :price="priceText"
              :unit="unitText"
              :show-negotiable="showNegotiableLabel"
            />
          </div>

          <div v-if="data.qty && data.qtyUnit" class="flex-1">
            <ProductUnit :qty="data.qty" :qty-unit="data.qtyUnit" />
          </div>
        </div>
      </div>
    </div>
    <template v-if="tags.show">
      <Divider class="my-20rpx" />
      <OutsideTagList :tags="tags" />
    </template>
    <MatchesLabel
      v-if="data.numberOfMatches"
      :number-of-matches="data.numberOfMatches"
      :match-text="styledText.matchText"
    />
  </div>
</template>

<script setup lang="ts">
import type { MatchTypes } from "@/api";
import MatchesLabel from "@/components/Trade/MatchesLabel.vue";
import ProductPrice from "@/components/Trade/ProductPrice.vue";
import OutsideTagList from "@/components/Trade/ProductTag/List/OutsideTagList.vue";
import ProductTitle from "@/components/Trade/ProductTitle.vue";
import ProductUnit from "@/components/Trade/ProductUnit.vue";
import SupplyThumbnail from "@/components/Trade/SupplyThumbnail.vue";

const props = defineProps<{ data: MatchTypes.ProductAppRsp }>();
const {
  styledText,
  publishTime,
  saleAddress,
  priceText,
  unitText,
  showImage,
  tags,
  showNegotiableLabel,
  toDetail,
} = useTradeProduct(props.data);
</script>

<style lang="scss" scoped>
view {
  --nut-divider-margin: 0;
}
</style>
