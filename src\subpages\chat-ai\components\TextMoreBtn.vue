<template>
  <image
    :src="ArrowLeftIcon"
    class="block h-40rpx w-40rpx border-1rpx border-#E0E0E0 rounded-full border-solid bg-#F4F5F7"
    :style="{ transform: `rotate(${rotate}deg)` }"
    @click="setShowMoreText"
  />
</template>

<script setup lang="ts">
import ArrowLeftIcon from "@/static/right-arrow.png";
import { useChatStore } from "@/store/modules/chat";

withDefaults(defineProps<{ rotate: number }>(), {
  rotate: 90,
});

const chatStore = useChatStore();
const { setShowMoreText } = chatStore;
</script>
