<template>
  <div
    class="f-c"
    :style="{
      height: `${menuButtonHeight}px`,
      marginRight: `${marginRight}px`,
    }"
  >
    <slot />
  </div>
</template>

<script setup lang="ts">
const { menuButtonHeight, menuButtonBounding, screenWidth } = storeToRefs(
  useAppStore(),
);
const marginRight = computed(() => {
  return (
    (menuButtonBounding.value?.width ?? 0) +
    (screenWidth.value - (menuButtonBounding.value?.right ?? 0))
  );
});
</script>
