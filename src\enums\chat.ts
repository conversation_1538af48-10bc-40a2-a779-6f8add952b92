export enum THOUGHT_CHAIN_ITEM_STATUS {
  // 等待状态
  PENDING = "pending",
  // 成功状态
  SUCCESS = "success",
  // 错误状态
  ERROR = "error",
}

export enum FeedbackStatus {
  Good = "Good",
  Bad = "Bad",
  Normal = "Normal",
}

// 工作流状态
export enum WorkflowsStatus {
  Running = "Running",
  Finished = "Finished",
}

// 工作流状态节点状态
export enum IWorkflowNodeStatus {
  Init = "Init",
  Running = "Running",
  Success = "Success",
  Error = "Error",
}

// 工作流状态节点状态
export enum IActionProcessStatus {
  tools = "tools",
  llm = "llm",
}

// 内容块类型
export enum ContentBlockTypeEnum {
  content = "content",
  agent_thought = "agent_thought",
  workflow = "workflow",
  file = "file",
  referrence = "referrence",
  think = "think",
  think_finished = "think_finished",
}

export enum RoleEnum {
  system_message = "system_message",
  user_message = "user_message",
  Assistant = "assistant",
}

export enum MessageStatusEnum {
  Loading = "loading",
  Streaming = "streaming",
  Finish = "finish",
  Error = "error",
}

export enum EventEnum {
  // 意图开始事件，代表一个新的意图处理开始
  THINK = "think",
  THINK_FINISHED = "think_finished",
  // 消息事件，代表普通消息的发送或接收
  MESSAGE = "message",
  // 消息文件事件，代表与消息相关的文件信息
  MESSAGE_FILE = "message_file",
  // 消息结束事件，代表一条消息的处理结束
  MESSAGE_END = "message_end",
  // 错误事件，代表系统出现错误的情况
  ERROR = "error",
  // 工作流开始事件，代表工作流开始执行
  WORKFLOW_STARTED = "workflow_started",
  // 工作流结束事件，代表工作流执行完成
  WORKFLOW_FINISHED = "workflow_finished",
  // 工作流节点开始事件，代表工作流中的某个节点开始执行
  WORKFLOW_NODE_STARTED = "node_started",
  // 工作流节点进行，代表工作流中的某个节点执行中
  WORKFLOW_NODE_WORKING = "node_working",
  // 工作流节点结束事件，代表工作流中的某个节点执行完成
  WORKFLOW_NODE_FINISHED = "node_finished",
}

// 订单状态
export enum OrderStatusEnum {
  All = "all", // 全部
  A00 = "A00", // 未开始
  B00 = "B00", // 未到港
  C10 = "C10", // 清关中
  C20 = "C20", // 清关完成
  C30 = "C30", // 已入库
  C40 = "C40", // 已出清
  C50 = "C50", // 已完成
}

// 清关状态
export enum ClearanceStatusEnum {
  ALL = "1", // 全部
  CLEARANCE = "2", // 清关中
  FINISH = "3", // 已放行
}

// 查验标记
export enum CustomsInsFlag {
  NO = "0",
  YES = "1",
  WAIT = "2",
}
