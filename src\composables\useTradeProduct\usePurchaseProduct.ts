import type { MatchTypes } from "@/api";
import { TradeProductType } from "@/enums";
import { omit } from "lodash";

export function usePurchaseProduct(data: MatchTypes.PurchaseProductAppRsp) {
  // 转换成通用商品类型
  const tradeProduct: MatchTypes.ProductAppRsp = {
    ...omit(data, ["tag"]),
    tagList: data.tag,
    productType: {
      code: TradeProductType.purchase,
    },
  };
  return useTradeProduct(tradeProduct);
}
