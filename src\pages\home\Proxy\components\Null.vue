<template>
  <CouponActivationTip
    :show="CouponActivationTipProps.show"
    :amount="CouponActivationTipProps.amount"
    :wait-active-count="CouponActivationTipProps.waitActiveCount"
  />
  <!-- <div
    v-if="isLogined && couponList.length"
    class="h-96rpx w-full flex items-center bg-contain bg-no-repeat"
    :style="{ backgroundImage: `url(${PROXY_IMAGES.couponBgd})` }"
  >
    <div
      class="w-150rpx flex items-end justify-center px-20rpx color-[#FF0000]"
    >
      <div class="text-[48rpx] fw-600 leading-[40rpx]">
        {{ amount > 10000 ? (amount / 10000).toFixed(1) : amount }}
      </div>
      <div class="text-[22rpx]">{{ amount > 10000 ? "万元" : "元" }}</div>
    </div>
    <div class="flex flex-col flex-1 color-white">
      <div class="text-[26rpx]">您有{{ couponList.length }}张优惠劵待激活</div>
      <div class="text-[22rpx]">激活领取后, 即可使用</div>
    </div>
    <div
      class="mr-20rpx h-56rpx w-140rpx rounded-[8rpx] text-center text-[26rpx] color-[#FF0000] leading-[56rpx]"
      style="background: linear-gradient(90deg, #f9fdef 0%, #ffedd1 100%)"
      @click="
        () => openWebview({ url: '/coupon-h5/pages/index?type=WAIT_ACTIVE' })
      "
    >
      点击激活
    </div>
  </div> -->

  <div class="null-container">
    <image
      mode="widthFix"
      :src="'agent_introduce_1.png'.toQiniuUrl()"
      class="h-full w-full"
    />
    <div class="rounded-card pt-44rpx">
      <image
        mode="widthFix"
        :src="'agent_introduce_2.png'.toQiniuUrl()"
        class="h-full w-full"
      />
    </div>
    <div class="rounded-card pt-26rpx">
      <image
        mode="widthFix"
        :src="'agent_introduce_3.png'.toQiniuUrl()"
        class="h-full w-full"
      />
      <image
        mode="widthFix"
        :src="'agent_introduce_4.png'.toQiniuUrl()"
        class="mt-48rpx h-full w-full"
      />
    </div>
    <div class="rounded-card pt-12rpx">
      <image
        mode="widthFix"
        :src="'agent_introduce_5.png'.toQiniuUrl()"
        class="h-full w-full"
      />
    </div>

    <div class="text-center">
      <image
        mode="widthFix"
        :src="'bottom-oig.png'.toQiniuUrl()"
        class="my-60rpx h-200rpx w-440rpx"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import type { CouponTypes } from "@/api";
import CouponActivationTip from "./CouponActivationTip.vue";

const userStore = useUserStore();
const { isLogined } = storeToRefs(userStore);

const { openWebview } = useNavigate({
  webview: { withToken: true, autoOrigin: true },
});

const couponList = ref<CouponTypes.ICouponRecordRsp[]>([]);

const CouponActivationTipProps = computed(() => {
  const amount = unref(couponList).reduce(
    (acc, cur) => acc + (cur?.couponAmount ? +cur.couponAmount : 0),
    0,
  );
  return {
    show: isLogined.value && unref(couponList).length > 0,
    amount,
    waitActiveCount: unref(couponList)?.length,
  };
});

// const amount = computed(() => {
//   return couponList.value.reduce(
//     (acc, cur) => acc + (cur?.couponAmount ? +cur.couponAmount : 0),
//     0,
//   );
// });

function getData() {
  couponApi
    .ApiCouponsPagelistPageLimitPost(
      { couponStatusEnum: "WAIT_ACTIVE" },
      { page: 1, limit: 999999 },
    )
    .then(res => {
      couponList.value = res?.data?.list || [];
    });
}

defineExpose({
  getData,
});
</script>
