<template>
  <div
    v-if="!isCouponSumListEmpty || (waitActiveCount ?? 0) > 0"
    class="mb-16rpx mt-14rpx border-rd-16rpx bg-white p-12px"
    @click="navigateToCouponPage"
  >
    <div class="grid grid-cols-3 gap-4px">
      <div class="grid-item">
        <div class="grid-item-top">
          <NumberWithUnit
            :show-decimal="false"
            :value="quarterOrderNum ?? 0"
            suffix="柜"
            integer-font-size="18px"
            suffix-color="#222528"
            decimal-font-size="10px"
          />
        </div>
        <div class="flex items-center text-12px">
          <span class="text-12px">本季度交柜量</span>
          <image
            :src="Bzzx"
            class="size-12px"
            @click.stop="showCouponCabinetDialog"
          />
        </div>
      </div>
      <div class="grid-item">
        <div class="grid-item-top">
          <template v-if="Number(couponStatistical?.quarterAmount)">
            <NumberWithUnit
              :show-decimal="false"
              :value="couponStatistical?.quarterAmount ?? 0"
              suffix="元"
              integer-font-size="18px"
              suffix-color="#222528"
              decimal-font-size="10px"
            />
          </template>
          <div v-else class="text-10px text-#222528">优惠等你来拿</div>
        </div>
        <div class="flex items-center text-12px">
          <span>本季度优惠金额</span>
          <image :src="Bzzx" class="size-12px" @click.stop="showCouponDialog" />
        </div>
      </div>
      <div class="grid-item">
        <div class="grid-item-top">
          <template v-if="Number(couponStatistical?.yearUseAmount)">
            <NumberWithUnit
              :show-decimal="false"
              :value="couponStatistical?.yearUseAmount ?? 0"
              suffix="元"
              integer-font-size="18px"
              suffix-color="#222528"
              decimal-font-size="10px"
            />
          </template>
          <div v-else class="text-10px text-#222528">优惠等你来拿</div>
        </div>
        <div class="text-12px">今年优惠金额</div>
      </div>
    </div>

    <!-- 进度条 -->
    <template v-if="!isCouponSumListEmpty">
      <!-- <div v-if="!isExceedMaxCabinetThreshold" class="progress"> -->
      <div class="progress">
        <div class="progress-bar-bg">
          <div class="progress-bar" :style="getProgressBarStyle" />
          <div v-if="hasGroupCode" class="progress-org">
            <span class="absolute top-13px text-10px text-#878C94">
              集团本季度 <span class="text-#FF3600">{{ groupQtOdNum }}</span> 柜
            </span>
          </div>
          <template v-for="(item, index) in couponsRange" :key="index">
            <div
              v-if="item?.cabinetThreshold"
              :style="getDotStyle(index)"
              class="progress-dot"
            />
            <div
              v-if="item?.number"
              class="progress-bubble"
              :class="lastCouponsRange ? 'last-range' : ''"
              :style="getBubbleStyle(index)"
            >
              <div class="progress-bubble-content">
                <image :src="CouponProgressAmount" class="size-10px" />
                <span class="text-10px">
                  可用{{ item.couponAmount }}x{{ item?.number }}张
                </span>
              </div>
            </div>
            <div
              v-if="item?.cabinetThreshold"
              class="progress-bottom"
              :style="getBottomStyle(index)"
            >
              {{ item?.cabinetThreshold }}柜
            </div>
          </template>
        </div>
      </div>

      <div v-if="!isExceedMaxCabinetThreshold" class="mt-10px text-12px">
        <span>{{ hasGroupCode ? "集团" : "" }}再交</span>
        <NumberWithUnit
          :value="distanceNextCouponThreshold"
          :show-decimal="false"
          color="#FF3600"
        />
        <span>柜，每柜优惠 </span>
        <NumberWithUnit
          :value="distanceNextCouponAmount"
          :show-decimal="false"
          color="#FF3600"
        />
        <span> 元</span>
      </div>

      <!-- <div v-if="isExceedMaxCabinetThreshold" class="done-box">
        <div class="h-30px flex items-center justify-between">
          <div class="flex items-center">
            <image :src="CouponProgressComplete" class="mr-4px size-16px" />
            <span class="text-16px text-#FF3600">恭喜您！</span>
          </div>
          <image :src="AgentCouponProgressDone" class="size-28px" />
        </div>
        <div class="text-12px text-#FF3600">
          <span>本季度交柜量已超</span>
          <NumberWithUnit
            :value="maxCabinetThreshold"
            :show-decimal="false"
            color="#FF3600"
          />
          <span>柜，预计累计优惠</span>
          <NumberWithUnit
            :value="couponSumAmount"
            :show-decimal="false"
            color="#FF3600"
          />
          <span>元</span>
        </div>
      </div> -->
    </template>

    <template v-if="isCouponSumListEmpty && showTip">
      <div class="h-12px" />
      <CouponActivationTip
        :show="CouponActivationTipProps.show"
        :amount="CouponActivationTipProps.amount"
        :wait-active-count="CouponActivationTipProps.waitActiveCount"
      />
    </template>
  </div>
  <nut-dialog
    v-model:visible="couponDialog"
    content="本季度期间，使用优惠券结算时的优惠金额"
    :ok-text="$t('wo-zhi-dao-le')"
    no-cancel-btn
  />
  <nut-dialog
    v-model:visible="couponCabinetDialog"
    content="本季度柜量指当前企业季度有效交柜量，集团本季度柜量指集团全部子公司季度交柜总量"
    :ok-text="$t('wo-zhi-dao-le')"
    no-cancel-btn
  />
</template>

<script setup lang="ts">
import type { CouponTypes } from "@/api";
import type { ICustomerCouponStatistical } from "youxian-api/src/youdingteCoupons/types";
import Bzzx from "@/static/bzzx.png";
import CouponProgressAmount from "@/static/coupon_progress_amount.png";
import { formatPercentage } from "@/utils/format";
import { useToggle } from "@vueuse/core";
import CouponActivationTip from "../CouponActivationTip.vue";
import NumberWithUnit from "../NumberWithUnit.vue";
import { useCouponsStatistical } from "./const";

defineOptions({
  name: "CouponCountCard",
});

withDefaults(
  defineProps<{
    // showCard?: boolean;
    // showProgress?: boolean;
    waitActiveCount?: number;
    showTip?: boolean;
  }>(),
  {
    // showCard: false,
    // showProgress: false,
    showTip: false,
  },
);
const userStore = useUserStore();
const { isLogined } = storeToRefs(userStore);

onBeforeMount(async () => {
  fetchData();
});

const couponStatistical = ref<ICustomerCouponStatistical>();
const {
  isCouponSumListEmpty,
  couponsRange,
  isExceedMaxCabinetThreshold,
  progressCabinetNum,
  lastCouponsRange,
  distanceNextCouponThreshold,
  distanceNextCouponAmount,
  maxCabinetThreshold,
  couponSumAmount,
  quarterOrderNum,
  hasGroupCode,
  groupQtOdNum,
} = useCouponsStatistical(couponStatistical);

const couponList = ref<CouponTypes.ICouponRecordRsp[]>([]);

const CouponActivationTipProps = computed(() => {
  const amount = unref(couponList).reduce(
    (acc, cur) => acc + (cur?.couponAmount ? +cur.couponAmount : 0),
    0,
  );
  return {
    show: isLogined.value && unref(couponList).length > 0,
    amount,
    waitActiveCount: unref(couponList)?.length,
  };
});

async function fetchData() {
  try {
    const { data } = await couponApi.ApiCouponsGetstatisticalGet();
    const { data: couponListRsp } =
      await couponApi.ApiCouponsPagelistPageLimitPost(
        { couponStatusEnum: "WAIT_ACTIVE" },
        { page: 1, limit: 999999 },
      );
    couponStatistical.value = data;
    couponList.value = couponListRsp?.list || [];
  } catch (error) {
    console.error(error);
  }
}

const [couponCabinetDialog, toggleCouponCabinetDialog] = useToggle(false);
function showCouponCabinetDialog() {
  toggleCouponCabinetDialog(true);
}

const [couponDialog, toggleCouponDialog] = useToggle(false);
function showCouponDialog() {
  toggleCouponDialog(true);
}

const getProgressBarStyle = computed(() => {
  let width = "0";
  const num1 = couponsRange.value?.[0]?.cabinetThreshold ?? 0;
  const num2 = couponsRange.value?.[1]?.cabinetThreshold ?? 0;
  const _progressCabinetNum = unref(progressCabinetNum);

  if (_progressCabinetNum === 0) {
    width = "0";
  } else if (_progressCabinetNum < num1) {
    const decimals = unref(lastCouponsRange)
      ? (_progressCabinetNum / num1) * 0.66
      : (_progressCabinetNum / num1) * 0.33;
    width = formatPercentage(decimals);
  } else if (_progressCabinetNum === num1) {
    width = unref(lastCouponsRange) ? "66%" : "33%";
  } else if (_progressCabinetNum > num1 && _progressCabinetNum < num2) {
    const diff = num2 - num1;
    const diff2 = _progressCabinetNum - num1;
    const baseDecimals = unref(lastCouponsRange) ? 0.66 : 0.33;
    width = `calc(${formatPercentage(
      (diff2 / diff) * 0.33 + baseDecimals,
    )} + 6px)`;
  } else if (_progressCabinetNum >= num2) {
    width = "100%";
  }
  return `width: ${width};`;
});

function getDotStyle(index: number) {
  const transform = "translateX(-3px) translateY(-3px)";
  const rightTransform = "translateX(-6px) translateY(-3px)";
  if (index === 0) {
    return {
      left: unref(lastCouponsRange) ? "66%" : "33%",
      transform,
    };
  } else if (index === 1) {
    return {
      left: unref(lastCouponsRange) ? "100%" : "66%",
      transform: unref(lastCouponsRange) ? rightTransform : transform,
    };
  }
}

function getBubbleStyle(index: number) {
  const transform = "translateX(-5px)";
  const rightTransform = "translateX(7px)";
  if (index === 0) {
    return {
      left: unref(lastCouponsRange) ? "" : "33%",
      right: unref(lastCouponsRange) ? "33%" : "",
      transform: unref(lastCouponsRange) ? rightTransform : transform,
    };
  } else if (index === 1) {
    return {
      left: unref(lastCouponsRange) ? "" : "66%",
      right: unref(lastCouponsRange) ? "0" : "",
      transform: unref(lastCouponsRange) ? rightTransform : transform,
    };
  }
}

function getBottomStyle(index: number) {
  const top = "13px";
  const transform = "translateX(-30%)";
  if (index === 0) {
    return {
      left: unref(lastCouponsRange) ? "66%" : "33%",
      top,
      transform,
    };
  } else if (index === 1) {
    return {
      left: unref(lastCouponsRange) ? "" : "66%",
      right: unref(lastCouponsRange) ? "0" : "",
      top,
      transform: unref(lastCouponsRange) ? "" : transform,
    };
  }
}

const { openWebview } = useNavigate({
  webview: { withToken: true, autoOrigin: true },
});
function navigateToCouponPage() {
  const url = "/coupon-h5/pages/history/index";
  openWebview({ url });
}

defineExpose({
  getData: fetchData,
});
</script>

<style scoped>
.grid-item {
  @apply h-40px flex flex-col items-center justify-between;
}

.grid-item-top {
  @apply h-22px flex items-end;
}

.progress {
  @apply relative;
  @apply box-border mt-9px h-68px rounded-4px bg-#FFF6F6;
}

.progress-bar-bg {
  @apply top-50% translate-y-50%;
  @apply relative h-6px bg-#F5CACA rounded-full mx-8px;
}

.progress-bar {
  @apply absolute h-6px bg-#FF3600 rounded-full;
}

.progress-dot {
  @apply absolute;
  @apply box-border size-12px rounded-full border-2px border-solid border-white bg-#FF3600;
}

.progress-bubble {
  @apply absolute -top-27px min-w-60px h-20px box-border bg-#FFF6F6 rounded-4px;
  @apply text-10px z-10;

  border: 0.5px solid #ff3600;
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
}

.progress-bubble::after {
  position: absolute;
  bottom: -4px;
  left: 4px;
  content: "";
  border-color: #ff3600 transparent transparent;
  border-style: solid;
  border-width: 3px 3px 0;
}

.progress-bubble.last-range::after {
  right: 4px;
  left: auto;
}

.progress-bubble::before {
  position: absolute;
  bottom: -3px;
  left: 4px;
  z-index: 1;
  content: "";
  border-color: white transparent transparent;
  border-style: solid;
  border-width: 3px 3px 0;
}

.progress-bubble.last-range::before {
  right: 4px;
  left: auto;
}

.progress-bubble-content {
  @apply h-full flex items-center px-4px gap-2px;
}

.progress-bottom {
  @apply absolute text-#878C94 text-10px z-11;
}

.done-box {
  @apply box-border px-10px pt-8px mt-9px h-68px rounded-4px bg-#FFF6F6;
}
</style>
