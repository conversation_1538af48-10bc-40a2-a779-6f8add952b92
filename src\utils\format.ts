/**
 * 格式化百分比
 * @param value 数值
 * @param decimals 小数位数
 * @returns 格式化后的百分比字符串
 */
export function formatPercentage(value: number, decimals: number = 0): string {
  if (Number.isNaN(value)) return "0%";
  return `${Math.min(Math.max(value * 100, 0), 100).toFixed(decimals)}%`;
}

/**
 * 格式化带符号的百分比
 * @param value 数值
 * @param decimals 小数位数
 * @returns 格式化后的百分比字符串
 */
export function formatPercentageWithSign(value: number, decimals: number = 0): string {
  const percentage = formatPercentage(value, decimals);
  return value > 0 ? `+${percentage}` : percentage;
}

// 截取小数点后 n 位，不做四舍五入
export function sliceNumberDecimal(num: number, decimal = 2) {
  const factor = 10 ** decimal;
  return Math.floor(num * factor) / factor;
}
