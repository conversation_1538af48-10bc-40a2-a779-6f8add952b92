<template>
  <div
    v-if="status === RealNameAuth.PENDING"
    class="realname-tag px-14rpx"
    @click="toRealNamePage"
  >
    {{ $t("shi-ming-zhong") }}
  </div>

  <div
    v-else-if="status === RealNameAuth.SUCCESS"
    class="realname-tag pl-10rpx pr-18rpx"
    @click="toProfile"
  >
    <image :src="RealNameDone" class="size-20rpx" />
    {{ $t("yi-shi-ming") }}
  </div>

  <div v-else-if="readonly" class="realname-tag px-14rpx">
    {{ $t("wei-shi-ming") }}
  </div>

  <div v-else class="realname-tag w-80rpx pl-12rpx" @click="toRealNamePage">
    {{ $t("qu-shi-ming") }}
    <!-- @unocss-skip-start -->
    <nut-icon
      name="right"
      :custom-color="unotheme.colors.primary"
      size="16rpx"
    />
    <!-- @unocss-skip-end -->
  </div>
</template>

<script setup lang="ts">
import { RealNameAuth } from "@/enums";
import RealNameDone from "@/static/realname_done.png";
import { unotheme } from "@/styles/themes/uno.theme";

defineOptions({
  options: { styleIsolation: "shared" },
});

defineProps<{ readonly?: boolean }>();

const { userInfo } = storeToRefs(useUserStore());
const status = computed(() => userInfo.value?.realnameAuthStatus);

const { toRealNamePage } = useNavigate();

function toRealnameForm() {
  uni.navigateTo({ url: "/subpages/realname/form" });
}
function toProfile() {
  uni.navigateTo({
    url: "/pages/profile/index",
  });
}
</script>

<style lang="scss" scoped>
.realname-tag {
  @apply ml-16rpx f-c circle text-17rpx color-primary solid-2rpx-#B4E6FF h-32rpx bg-#fff;

  position: relative;
  top: 2px;
  display: flex;
  gap: 3px;
  align-content: center;
}

::v-deep .nut-icon {
  position: absolute;
  right: 0;
}
</style>
