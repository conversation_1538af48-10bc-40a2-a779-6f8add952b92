<template>
  <div
    class="mt-24rpx h-72rpx w-658rpx flex items-center rounded-8rpx bg-[linear-gradient(270deg,#fff_0%,#eaf7ff_100%)] px-14rpx"
  >
    <image :src="ConnectionIcon" class="h-46rpx w-46rpx" />
    <div class="ml-16rpx text-26rpx c-trade-supply lh-36rpx">
      {{ $t("pi-pei") }}
      <span class="mx-4rpx c-trade-purchase">{{ numberOfMatches }}</span>
      {{ matchText }}
    </div>
  </div>
</template>

<script setup lang="ts">
import ConnectionIcon from "@/static/connection.png";

const { numberOfMatches = 0 } = defineProps<{
  matchText: string;
  numberOfMatches?: number;
}>();
</script>
