import { ftPlugin } from "oig-finclip-jssdk/weapp";

export class AppIdConfig {
  private test = "fc2360656611291973";
  private pre = "fc2612300485280389";
  private prod = "fc2380318133291717";
  public current = "";

  constructor() {
    this.init();
  }

  async init() {
    if (ftPlugin.inFinClip ?? false) {
      // APP 环境
      const { env } = await ftPlugin?.getNativeEnv();
      if (env === "test") {
        this.current = this.test;
      } else if (env === "petout") {
        this.current = this.pre;
      } else if (env === "production") {
        this.current = this.prod;
      }
    }
  }
}

export const chatAppIdConfig = new AppIdConfig();
