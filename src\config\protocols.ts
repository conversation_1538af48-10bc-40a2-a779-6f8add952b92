export interface ProtocolModel {
  title: string;
  url: string;
}

export class YdtProtocols {
  static register: ProtocolModel = {
    title: t("yong-hu-zhu-ce-xie-yi"),
    url: "/landing/ydt-cn/register.html",
  };

  static privacy: ProtocolModel = {
    title: t("you-ding-te-yin-si-zheng-ce"),
    url: "/landing/ydt-cn/privacy.html",
  };

  static about: ProtocolModel = {
    title: t("guan-yu-you-ding-te"),
    url: "/landing/ydt-cn/about.html",
  };
}
