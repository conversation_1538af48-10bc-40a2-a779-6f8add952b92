<template>
  <TopMenuBar class="ml-48rpx f-c">
    <div
      v-for="tab in tabs"
      :key="tab.value"
      :class="tab.value === value ? activeClassName : 'opacity-60'"
      class="relative mr-48rpx text-32rpx"
      @click="onClick(tab.value)"
    >
      {{ tab.name }}
      <image
        v-if="tab.value === value"
        class="absolute bottom-4rpx left-0 z-[-1] h-18rpx w-full"
        src="@/static/tab_selected.png"
      />
    </div>
  </TopMenuBar>
</template>

<script setup lang="ts">
import { HomeTabEnum } from "../useHomeView";

const emit = defineEmits<{ change: [value: HomeTabEnum] }>();
const value = defineModel<HomeTabEnum>({ required: true });
const activeClassName = "text-36rpx font-semibold opacity-100";
const tabs = ref([
  {
    name: t("shi-chang"),
    value: HomeTabEnum.Mall,
  },
  {
    name: t("dai-li"),
    value: HomeTabEnum.Porxy,
  },
]);

function onClick(tab: HomeTabEnum) {
  value.value = tab;
  emit("change", tab);
}
</script>
