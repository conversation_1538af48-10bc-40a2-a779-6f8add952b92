<template>
  <div v-if="bannerList.length > 0" class="my-[16rpx] h-[168rpx]">
    <swiper
      autoplay
      circular
      disable-touch
      :touchable="false"
      class="h-[168rpx]"
      :indicator-dots="bannerList.length > 1"
      :indicator-color="unotheme.colors.primary"
    >
      <swiper-item v-for="item in bannerList" :key="item.id">
        <div
          class="h-[168rpx] rounded bg-cover bg-no-repeat"
          :style="{ backgroundImage: `url(${item.imgUrl})` }"
          @click="unknownUrlNavigate(item.link)"
        />
      </swiper-item>
    </swiper>
  </div>
</template>

<script setup lang="ts">
import type { PlatformTypes } from "@/api";
import { AuthConfig } from "@/config";
import { unotheme } from "@/styles/themes/uno.theme";

defineOptions({
  options: { styleIsolation: "shared" },
});

const KEY_HOME_BANNER = "homeBanner";
const BANNER_POSITION = "TOP";
const bannerList = ref<PlatformTypes.IPlatformBannerVo[]>([]);
const { unknownUrlNavigate } = useNavigate();

const [fetchBanners] = useApiFetch(() =>
  platformApi
    .ApiBannerListbypositionPost({
      appId: AuthConfig.appId,
      showPositions: [BANNER_POSITION],
      displayTerminal: "MOBILE_TERMINAL",
    })
    .then(rsp => {
      const target = rsp.data.find(
        item => item.showPosition === BANNER_POSITION,
      );
      if (target) {
        bannerList.value = target.bannerList ?? [];
        uni.setStorageSync(KEY_HOME_BANNER, JSON.stringify(bannerList.value));
      }
    }),
);

function tryInitStroage(): PlatformTypes.IPlatformBannerVo[] {
  const storageData = uni.getStorageSync(KEY_HOME_BANNER);
  if (storageData) {
    try {
      bannerList.value = JSON.parse(storageData);
    } catch {}
  }

  return [];
}

defineExpose({
  fetchBanners,
});

onBeforeMount(() => {
  tryInitStroage();
  fetchBanners();
});
</script>

<style lang="scss" scoped>
::v-deep .nut-swiper-pagination {
  bottom: 8rpx;
}
</style>
