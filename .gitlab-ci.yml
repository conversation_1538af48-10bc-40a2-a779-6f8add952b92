stages:
  - build
  - deploy

include:
  remote: "http://***************/FRONT/oig-cli-shared-utils/-/raw/master/gitlab-ci-front-tpl.yml"

build:
  extends:
    - .include_build_server
  stage: build
  only:
    refs:
      - main
      - tags
    changes:
      - "src/subpages/chat-ai/**/*"
  script:
    - node -v
    - npm install pnpm -g
    - pnpm install
    - pnpm run api
    - pnpm run build:mp-weixin:chat
  artifacts:
    when: on_success
    expire_in: 1 day
    paths:
      - dist/

deploy_finclip_chat:
  extends:
    - .include_build_server
  only:
    refs:
      - main
      - tags
    changes:
      - "src/subpages/chat-ai/**/*"
  stage: deploy
  needs:
    - "build"
  script:
    - >
      if [ "$CI_COMMIT_BRANCH" == "main" ]; then
          build-server build:finclip --mode test;
      elif [ -n "$CI_COMMIT_TAG" ]; then
        if [[ "$CI_COMMIT_TAG" == *pre* ]]; then
          build-server build:finclip --mode pre;
        elif  [[ "$CI_COMMIT_TAG" == *prod* ]]; then
          build-server build:finclip --mode prod;
        fi
      fi
