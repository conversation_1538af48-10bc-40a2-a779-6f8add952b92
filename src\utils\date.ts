import dayjs from "dayjs";
import isTodayPlugin from "dayjs/plugin/isToday";

dayjs.extend(isTodayPlugin);

type DateType = Parameters<typeof dayjs>[0];

export function formatDate(
  date?: DateType,
  format: string = "YYYY-MM-DD",
): string {
  return dayjs(date).format(format);
}

export function formatYM(date?: DateType): string {
  return formatDate(date, "YYYY-MM");
}
export function formatDateTime(date?: DateType): string {
  return formatDate(date, "YYYY-MM-DD HH:mm:ss");
}

export function formatDateTimeyMdhm(date?: DateType): string {
  return formatDate(date, "YYYY-MM-DD HH:mm");
}

export function isToday(date?: DateType) {
  if (!date) {
    return false;
  }

  return dayjs(date).isToday();
}
