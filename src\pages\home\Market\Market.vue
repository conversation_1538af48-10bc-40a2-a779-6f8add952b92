<template>
  <scroll-view
    :style="{ height: scrollViewHeight }"
    scroll-y
    refresher-enabled
    :refresher-triggered="refreshing"
    :refresher-threshold="500"
    :lower-threshold="100"
    @scrolltolower="onLoadMore"
    @refresherrefresh="onRefresh"
  >
    <GridMenu ref="gridMenu" />
    <Banner ref="banner" />
    <Report ref="report" />
    <TradeList ref="tradeList" />
  </scroll-view>
</template>

<script setup lang="ts">
import { useHomeView } from "../useHomeView";
import Banner from "./components/Banner.vue";
import GridMenu from "./components/GridMenu.vue";
import Report from "./components/Report/index.vue";
import TradeList from "./components/TradeList/index.vue";

const { scrollViewHeight, refreshing, refreshHomeView } = useHomeView();
const tradeList = ref<InstanceType<typeof TradeList> | null>(null);
const gridMenu = ref<InstanceType<typeof GridMenu> | null>(null);
const banner = ref<InstanceType<typeof Banner> | null>(null);
const report = ref<InstanceType<typeof Report> | null>(null);

function onLoadMore() {
  tradeList.value?.dispatchFetch(FetchStrategy.more);
}

function onRefresh() {
  refreshHomeView(() => {
    banner.value?.fetchBanners();
    gridMenu.value?.fetchGridAppList();
    report.value?.fetchAll();
    tradeList.value?.dispatchFetch(FetchStrategy.refresh);
  });
}
</script>
