<template>
  <div class="mb-20px">
    <SearchResultTilte>{{ $t("jiao-yi") }}</SearchResultTilte>
    <div class="m-20rpx">
      <TradeCard v-for="item in products" :key="item.id" :data="item" />
    </div>
  </div>
</template>

<script setup lang="ts">
import type { MatchTypes } from "@/api";
import TradeCard from "@/pages/home/<USER>/components/TradeCard/index.vue";
import SearchResultTilte from "../SearchResultTilte.vue";

defineProps<{
  products?: MatchTypes.ProductAppRsp[];
}>();
</script>
