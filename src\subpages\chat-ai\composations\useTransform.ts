import type * as ChatTypes from "@/api/servers/chat/type";
import type { CardTypeEnum } from "@/enums";
import type { EventEnum, IActionProcessStatus } from "@/enums/chat";
import {
  ContentBlockTypeEnum,
  IWorkflowNodeStatus,
  MessageStatusEnum,
  RoleEnum,
  WorkflowsStatus,
} from "@/enums/chat";

export interface ParsedDataData {
  // 工作流节点的数据
  id: string;
  node_type?: ChatTypes.IWorkflowNode["type"];
  title?: string;
  inputs: string;
  outputs: any;
  action_process?: ChatTypes.IActionProcessItem;
  action_type?: IActionProcessStatus;
  process_data: string;
  elapsed_time: number;
  execution_metadata?: ChatTypes.IWorkflowNode["execution_metadata"];
  duration?: number;
  is_card?: boolean;
  card_type?: CardTypeEnum;
  ai_search_key?: string;
  event_type?: EventEnum; // 事件类型
}

export interface ParsedData {
  id?: string;
  task_id?: string;
  position?: number;
  tool?: string;
  tool_input?: string;
  observation?: string;
  message_files?: string[];
  event?: EventEnum;
  answer?: string;
  conversation_id: string;
  message_id?: string;
  intention_id?: string;
  // 类型
  type?: "image";
  // 图片链接
  url?: string;
  data: ParsedDataData;
}

export function useTransform() {
  // 流式数据归一化
  const streamTransform = (input: ChatTypes.ChunkData): ParsedData => {
    const { message, event_type } = input;

    return {
      conversation_id: message?.session_id.toString(),
      message_id: message?.message_id.toString(),
      data: {
        id: message.id,
        node_type: "question-classifier",
        title: message.description,
        inputs: "",
        outputs: message.content || "",
        process_data: "",
        elapsed_time: message.duration,
        event_type: message.event_type,
        is_card: message.is_card,
      },
      answer: message.content,
      tool: message.content,
      tool_input: message.content,
      observation: message.content,
      event: event_type,
    };
  };

  // 会话历史数据归一化
  const messagesTransform = (
    messages: ChatTypes.IMessage[],
  ): ChatTypes.ChatMessage[] => {
    const newMessages: ChatTypes.ChatMessage[] = [];
    messages.forEach((item: ChatTypes.IMessage) => {
      if (item.event_type === RoleEnum.user_message) {
        newMessages.push({
          id: String(item.message_id),
          content: item.content || "",
          status: MessageStatusEnum.Finish,
          role: RoleEnum.user_message,
          created_time: item.created_time,
          intention_id: item.intention_id || 0,
        });
      } else if (item.event_type === RoleEnum.system_message) {
        const handlePlan: ChatTypes.ContentBlock[] =
          item.plans?.map((item: ChatTypes.Plans) => {
            return {
              type: ContentBlockTypeEnum.workflow,
              content: "",
              workflows: {
                status: WorkflowsStatus.Finished,
                nodes: item.actions?.map((subItem: ChatTypes.Action) => {
                  const action_result = subItem.action_result?.action_result;
                  let actionContent = "";
                  let cardList = [];
                  if (action_result?.is_card) {
                    cardList = action_result.action_content as any[];
                  } else {
                    actionContent = action_result?.action_content || "";
                  }
                  return {
                    id: String(subItem.action_id),
                    status: IWorkflowNodeStatus.Success,
                    type: "question-classifier",
                    action_type: subItem?.action_type,
                    action_process: subItem.action_process,
                    title: subItem.action_description || "",
                    outputs: action_result || "",
                    elapsed_time: subItem.action_duration,
                    actionContent,
                    cardList,
                    is_card: action_result.is_card,
                    card_type: action_result.card_type,
                    ai_search_key: action_result.ai_search_key,
                    actions: subItem?.actions?.map(childItem => {
                      return {
                        id: String(childItem.action_id),
                        status: IWorkflowNodeStatus.Success,
                        type: "question-classifier",
                        action_type: childItem?.action_type,
                        action_process: childItem.action_process,
                        title: childItem.action_description || "",
                        outputs: childItem.action_result?.action_result || "",
                        elapsed_time: childItem.action_duration,
                      };
                    }),
                  };
                }),
              },
            };
          }) || [];

        const contentBlocks: ChatTypes.ContentBlock[] = [
          {
            type: ContentBlockTypeEnum.think,
            content: item.intention_comprehend || "",
            isThink: true,
            workflows: {
              status: WorkflowsStatus.Running,
              nodes: [],
            },
          },
          {
            type: ContentBlockTypeEnum.content,
            content: item.intention_think || "",
            isThink: true,
            workflows: {
              status: WorkflowsStatus.Running,
              nodes: [],
            },
          },
          ...handlePlan,
        ];

        newMessages.push({
          id: String(item.intention_id),
          content: `${item.intention_comprehend || ""}${
            item.intention_think || ""
          }\n${item.intention_result || ""}`,
          status: MessageStatusEnum.Finish,
          error: false,
          isHistory: true,
          feedback: item.is_good,
          role: RoleEnum.Assistant,
          contentBlocks,
          created_time: item.created_time,
          intention_id: item.intention_id || 0,
        });
      }
    });
    return newMessages;
  };

  return {
    streamTransform,
    messagesTransform,
  };
}
