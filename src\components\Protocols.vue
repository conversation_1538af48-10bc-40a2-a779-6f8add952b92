<template>
  <div
    v-for="(protocol, index) in protocols"
    :key="protocol.url"
    class="inline"
  >
    <div class="inline color-primary" @click.stop="open(protocol)">
      《{{ protocol.title }}》
    </div>
    <span v-if="index < protocols.length - 1">、</span>
  </div>
</template>

<script setup lang="ts">
import type { ProtocolModel } from "@/config/protocols";

defineProps<{ protocols: ProtocolModel[] }>();
const { openWebview } = useNavigate();

function open(protocol: ProtocolModel) {
  openWebview({
    url: protocol.url,
    title: protocol.title,
    autoOrigin: true,
  });
}
</script>
