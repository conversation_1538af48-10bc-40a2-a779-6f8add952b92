import { isNil } from "lodash";
import { objectToQueryString, parseUrl } from "../url";

type ParamsValue = string | number | boolean;

function toParamsUrl(
  this: string,
  params: Record<string, ParamsValue | undefined>,
) {
  const queryObject: Record<string, ParamsValue> = {};
  // 过滤无效值
  for (const key in params) {
    if (!isNil(params[key]) && params[key] !== "") {
      queryObject[key] = params[key];
    }
  }
  const queryString = objectToQueryString(queryObject);
  if (!queryString) return this;
  const { query } = parseUrl(this);
  if (Object.keys(query).length > 0) {
    return `${this}&${queryString}`;
  } else {
    return `${this}?${queryString}`;
  }
}

String.prototype.toParamsUrl = toParamsUrl;
export type ToParamsUrl = typeof toParamsUrl;
