<template>
  <div
    v-for="intro in introduction"
    :key="intro[0]"
    class="mb-10rpx f-c justify-between text-28rpx"
  >
    <div>{{ TRADE_TAG_LABEL[intro[0]] }}</div>
    <div>{{ intro[1] }}</div>
  </div>
</template>

<script setup lang="ts">
import type { TRADE_TAG_KEYS } from "@/enums";
import { TRADE_TAG_LABEL } from "@/enums";

const buildDes = (min: number, max: number) => `出厂${min}-${max}个月`;

const introduction: Array<[TRADE_TAG_KEYS, string]> = [
  ["NEW_GOODS", buildDes(1, 6)],
  ["USED_GOODS", buildDes(6, 12)],
  ["LONG_DATED_GOODS", buildDes(12, 18)],
  ["NEAR_EXPIRY_GOODS", buildDes(18, 24)],
];
</script>
