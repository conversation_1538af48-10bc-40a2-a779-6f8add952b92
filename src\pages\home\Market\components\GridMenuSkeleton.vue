<template>
  <div class="grid-menu-card">
    <nut-grid :column-num="cols" :border="false">
      <nut-grid-item v-for="item in count" :key="item" class="f-c-c">
        <nut-skeleton
          width="62rpx"
          height="62rpx"
          animated
          round
          :title="false"
        />
        <div class="mt-4rpx">
          <nut-skeleton width="80rpx" height="20rpx" animated :title="false" />
        </div>
      </nut-grid-item>
    </nut-grid>
  </div>
</template>

<script setup lang="ts">
defineProps<{ cols: number }>();

const count = 10;
</script>
