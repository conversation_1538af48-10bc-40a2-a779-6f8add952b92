import type { PlatformTypes } from "@/api";
import { GridAppType, GridFunctionType, YesOrNo } from "@/enums";
import { addQueryParam } from "@/utils/url";

type Callback = Pick<
  ReturnType<typeof useNavigate>,
  "openWebview" | "withLogin"
>;

interface ExtraParam {
  usePosition: boolean;
  positionType: string;
}

// 金刚区跳转
export function useGridAppNavigate(callback: Callback) {
  const { getLocation } = useWxPermission();

  function handleGridAppNavigate(app: PlatformTypes.IAppFunctionByTypeVo) {
    callback
      .withLogin({
        when: () =>
          app.needLogin === YesOrNo.yes ||
          app.appInfoVo?.needToken === YesOrNo.yes,
      })
      .run(async () => {
        const extraParam = parseExtraParam(app.jsonData);
        if (extraParam) {
          await preprocess(app, extraParam);
        }
        dispatchNavigate(app);
      });
  }

  // 解析额外参数
  function parseExtraParam(jsonData?: string): ExtraParam | null {
    if (!jsonData) return null;
    let raw: any;
    const result: ExtraParam = {
      usePosition: false,
      positionType: "",
    };
    try {
      raw = JSON.parse(jsonData);
      result.usePosition = raw.usePosition === true;
      result.positionType = raw.positionType || "wgs84";
    } catch {}

    return result;
  }

  // 跳转前的预处理
  async function preprocess(
    app: PlatformTypes.IAppFunctionByTypeVo,
    extraParam: ExtraParam,
  ) {
    if (extraParam.usePosition) {
      const position = await getLocation(extraParam.positionType);
      if (app.url) {
        app.url = addQueryParam(app.url, "latitude", position.latitude);
        app.url = addQueryParam(app.url, "longitude", position.longitude);
      }
    }
  }

  function dispatchNavigate(app: PlatformTypes.IAppFunctionByTypeVo) {
    const { token } = useUserStore();
    const needToken = app.needToken === YesOrNo.yes;

    if (app.appFunctionType === GridFunctionType.outside) {
      if (app.outsideAppType === GridAppType.miniProgram && app.appId) {
        uni.navigateToMiniProgram({
          appId: app.appId,
          path: app.path,
          envVersion: wx.getAccountInfoSync().miniProgram.envVersion,
          extraData: needToken
            ? {
                [XHeader.HToken]: token,
              }
            : {},
        });
      } else {
        if (app.url) {
          const url = app.appletParam
            ? app.url + app.appletParam
            : app.url + app.path;
          callback.openWebview({
            title: app.functionName ?? "",
            url,
            withToken: needToken,
          });
        }
      }
    } else {
      if (app.appInfoVo?.appType === GridAppType.webview) {
        if (app.url && app.path) {
          const url = app.appletParam
            ? app.url + app.appletParam
            : app.url + app.path;
          callback.openWebview({
            title: app.functionName ?? "",
            url,
            withToken: needToken,
          });
        }
      } else if (
        app.appInfoVo?.appType === GridAppType.miniProgram &&
        app.appId
      ) {
        const localAppId = wx.getAccountInfoSync()?.miniProgram?.appId;
        if (app.appId === localAppId) {
          if (!app.path) {
            return showMsg("跳转地址有误，请稍后再试");
          }
          uni.navigateTo({
            url: app.path,
          });
        } else {
          uni.navigateToMiniProgram({
            appId: app.appId,
            path: app.path,
            envVersion: wx.getAccountInfoSync().miniProgram.envVersion,
            extraData: needToken
              ? {
                  [XHeader.HToken]: token,
                }
              : {},
          });
        }
      }
    }
  }

  return {
    handleGridAppNavigate,
  };
}
