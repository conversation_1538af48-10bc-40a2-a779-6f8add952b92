import axios from "axios";

import { adapter } from "./adapter";
import { timeout } from "./constant";
import { errorAdapter } from "./interceptors/error";
import { requestInterceptor } from "./interceptors/request";
import { responseAdapter } from "./interceptors/response";

export * from "./constant";
export { agentCustomer<PERSON>pi } from "./servers/agentCustomer";
export { chatApi } from "./servers/chat/index";
export { couponApi } from "./servers/coupon";
export { matchApi } from "./servers/match";
export { merchantApi } from "./servers/merchant";
export { newsAppApi } from "./servers/newCenter";
export { ocrApi } from "./servers/ocr";
export { platformApi } from "./servers/platform";
export { queryBackendApi } from "./servers/queryBackend";
export { userApi } from "./servers/user";

axios.defaults.timeout = timeout;
axios.defaults.adapter = adapter;
axios.interceptors.request.use(config => requestInterceptor(config));
axios.interceptors.response.use(responseAdapter, errorAdapter);

export type * as ChatTypes from "@/api/servers/chat/type";
export type * as MatchTypes from "matchmaking-api/src/base-server";
export type * as NewsAppTypes from "news-api/src/app/types";
export type * as QueryBackendTypes from "news-api/src/queryBackend/types";
export type * as AgentCustomerTypes from "worldrou-api/src/youdingteAgentCustomer/types";
export type * as PlatformTypes from "youxian-api/src/app/types";
export type * as MerchantTypes from "youxian-api/src/merchant/types";
export type * as OcrTypes from "youxian-api/src/ocr/types";
export type * as UserTypes from "youxian-api/src/user/types";
export type * as CouponTypes from "youxian-api/src/youdingteCoupons/types";
