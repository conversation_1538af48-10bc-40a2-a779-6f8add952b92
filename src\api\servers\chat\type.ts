import type { Card<PERSON><PERSON><PERSON>num } from "@/enums";
import type {
  ContentBlockTypeEnum,
  EventEnum,
  FeedbackStatus,
  IActionProcessStatus,
  IWorkflowNodeStatus,
  MessageStatusEnum,
  RoleEnum,
  THOUGHT_CHAIN_ITEM_STATUS,
  WorkflowsStatus,
} from "@/enums/chat";

export interface ChatMessage {
  id: string;
  role: RoleEnum;
  content: string;
  status: MessageStatusEnum;
  feedback?: FeedbackStatus;
  file?: string;
  query?: string;
  date?: string;
  error?: boolean;
  contentBlocks?: ContentBlock[];
  isHistory?: boolean;
  created_time?: string;
  intention_id: number;
}

export type PartialField<T, K extends keyof T> = Partial<Pick<T, K>> &
  Omit<T, K>;

export type addChatMessage = PartialField<ChatMessage, "id" | "content">;

export type updateChatMessage = Partial<Omit<ChatMessage, "id">>;

export interface SystemDefaultDataType {
  header_context?: string;
  header_title?: string;
  questions?: SystemDefaultDataQuestionsType[];
  thumbs_down?: string[] | [];
  thumbs_up?: string[] | [];
  voice_enabled?: boolean;
  file_enabled?: boolean;
  version?: string;
}

export interface SystemDefaultDataQuestionsType {
  title?: string;
  question?: string;
}

export interface MessageReq {
  user_id?: string | number;
  prompt?: string;
  session_id?: string;
  attachment?: string[];
  chat_type?: string; // 支持传入image, 图生成
}

export interface SystemUsageType {
  content?: string;
  title?: string;
  total?: number;
  used?: number;
}

// 用户会话
export interface SessionReq {
  user_id?: string | number;
}

export interface Sessions {
  session_id: string;
  group?: string;
  user_id?: number;
  session_name: string;
  created_time?: string;
  updated_time?: string;
}

// 用户会话信息
export interface UserSession {
  user_id: number;
  username: string;
  email: string;
  password: string;
  created_time: string;
  updated_time: string;
  sessions: Sessions[];
}

// 获取会话消息信息
export interface GetSessionInfoReq {
  session_id?: number | string;
  user_id?: number | string;
}
export interface GetSessionInfoRes {
  session_id?: number;
  user_id?: number;
  session_name?: string;
  created_time?: string;
  updated_time?: string;
  messages?: Array<IMessage>;
}
export interface IMessage {
  event_type: string;
  message_id: number;
  user_id: number;
  session_id: number;
  content?: string;
  created_time: string;
  updated_time: string;
  group?: string;
  intention_id?: number;
  intention_comprehend: any;
  intention_think: any;
  intention_result: any;
  intention_status?: string;
  plans?: Array<Plans>;
  is_good?: FeedbackStatus;
}
export interface Plans {
  plan_id: number;
  user_id: number;
  session_id: number;
  message_id: number;
  intention_id: number;
  plan_name: string;
  plan_description: any;
  plan_think: any;
  plan_result: any;
  plan_status: string;
  created_time: string;
  updated_time: string;
  actions: Array<Action>;
}
export interface Action {
  action_id: number;
  user_id: number;
  session_id: number;
  message_id: number;
  intention_id: number;
  plan_id: number;
  action_type: IWorkflowNode["action_type"];
  action_name: string;
  action_description: any;
  action_process: IActionProcess;
  action_result: any;
  action_status: string;
  actions?: Array<Action>;
  created_time: string;
  updated_time: string;
  action_duration: number;
}

export interface FeedbackReq {
  intention_id?: number;
  user_comment?: string;
  is_good?: FeedbackStatus;
}

// 取消对话
export interface CancelAgentReq {
  message_id: string;
}

export interface Message {
  session_id: string;
  message_id: string;
  intention_id: number;
  event_time: number;
  content: string;
  think: string;
  description: string;
  id: string;
  duration: number;
  is_card: boolean;
  event_type: EventEnum;
}
export interface ChunkData {
  message: Message;
  event_type?: EventEnum;
}

// 获取提示词
export interface GetXyTipsRes {
  user_id?: string | number;
}

export interface GetXyTipsReq {
  search_prompt?: SearchPrompt;
  oper_prompt?: OperPrompt;
  tips?: string;
}

export interface PromptList {
  label: string;
  prompt: string;
}
export interface SearchPrompt {
  type: string;
  prompt_list: PromptList[];
}

export interface OperPrompt {
  type: string;
  prompt_list: PromptList[];
}

export interface Outputs {
  action_id?: number;
  tool_name?: string;
  event_id?: string;
  action_status?: string;
  action_content?: ActionContent[];
  content?: ActionContent;
  total?: number;
  ai_search_key?: string;
  user_input?: string;
  is_card?: number;
  card_type?: string;
  status?: string;
}

export interface ActionContent {
  orderId?: string;
  contract?: string;
  cabinetNo?: string;
  allAmount?: string;
  finalVoyageEta?: string;
  portName?: string;
  orderStatus?: string;
  countryName?: string;
  factoryCode?: string;
  goodsName?: string;
}

/**
 * 工作流节点数据
 */
export interface IWorkflowNode {
  /**
   * 步骤 ID
   */
  id: string;
  /**
   * 步骤标题
   */
  title?: string;
  /**
   * 运行状态
   */
  status?: IWorkflowNodeStatus;
  /**
   * 节点类型 question-classifier/问题分类器
   */
  type: "question-classifier";
  /**
   * 节点输入 序列化的 JSON 数据
   */
  inputs?: string;
  /**
   * 处理过程 序列化的 JSON 数据
   */
  process_data?: string;
  /**
   * 节点输出 序列化的 JSON 数据
   */
  outputs?: Outputs;
  /**
   * 节点输出 卡片 JSON 数据
   */
  cardList?: any[];
  /**
   * 节点输出 文字
   */
  actionContent?: string;
  /**
   * 节点输出类型
   */
  event_type?: string;
  /**
   * 是否是卡片
   */
  is_card?: boolean;
  /**
   * 卡片查询条件
   */
  ai_search_key?: string;
  /**
   * 卡片类型
   */
  card_type?: CardTypeEnum;
  /**
   * 耗时 单位秒
   */
  elapsed_time?: number;
  action_type?: "normal" | "sub_action_framework";
  action_process?: IActionProcess;
  actions?: IWorkflowNode[];
  /**
   * 执行元数据
   */
  execution_metadata?: {
    /**
     * 总共消耗 tokens
     */
    total_tokens: number;
    /**
     * 总共消耗金额
     */
    total_price: number;
    /**
     * 货币单位
     */
    currency: string;
  };
}

/**
 * 工作流节点过程
 */
export interface IActionProcess {
  action_type: IActionProcessStatus;
  action_process: IActionProcessItem[] | string;
}

export interface IActionProcessItem {
  title?: string;
  content?: string;
  id?: string;
  status?: THOUGHT_CHAIN_ITEM_STATUS;
  description?: string;
  search_content?: ISearchContent[];
  action_type?: IActionProcessStatus;
}

export interface ISearchContent {
  title?: string;
  url?: string;
  body?: string;
}

export interface ContentBlock {
  /**
   * 内容块类型，如 'content', 'agent_thought', 'workflow', 'file', 'referrence' 等
   */
  type: ContentBlockTypeEnum;
  /**
   * 工作流信息
   */
  workflows: {
    /**
     * 整个工作流的运行状态 running-运行中，finished-已完成
     */
    status: WorkflowsStatus;
    /**
     * 工作流的节点详细信息
     */
    nodes: IWorkflowNode[];
  };
  /**
   * 消息主体内容
   */
  content?: string;
  isThink?: boolean;
  /**
   * 输入变量
   */
  inputs?: Record<string, string>;
}

export interface IAgentMessage {
  /**
   * 接口请求状态
   */
  requestStatus?: MessageStatusEnum;
  message_id?: string;
  content?: string;
  /**
   * 按接收到的顺序存储不同类型的内容块
   */
  contentBlocks?: ContentBlock[];
}

export interface IAgentError {
  name: string;
  message: string;
}
