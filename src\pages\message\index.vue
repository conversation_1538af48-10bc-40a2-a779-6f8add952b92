<template>
  <view class="h-100vh overflow-hidden">
    <div
      class="f-c-c bg-white"
      :style="{
        paddingTop: `${menuButtonBounding?.top}px`,
        height: `${menuButtonHeight}px`,
      }"
    >
      消息
    </div>
    <MsgTab ref="msgTab" v-model="activeTab" />
    <ScrollList
      :state="msgState"
      :scroll-view-height="scrollViewHeight"
      :refresher-enabled="true"
      :empty="list.length === 0"
      @on-load-more="onLoadMore"
      @on-refresh="onRefresh"
    >
      <view class="mt-20rpx px-24rpx">
        <CardItem
          v-for="item in list"
          :key="item.id"
          :data="item"
          @click="toDetail(item)"
        />
      </view>
    </ScrollList>
  </view>
</template>

<script setup lang="ts">
import type { PlatformTypes } from "@/api";
import { FetchStrategy } from "@/composables/useListFetch";
import { MessageTabEnum } from "@/enums";
import { delay } from "oig-utils";
import { NavigationTabEnum } from "../index/type";
import CardItem from "./components/CardItem.vue";
import MsgTab from "./components/MsgTab.vue";

const props = defineProps<{
  bottomTab: NavigationTabEnum;
}>();
const msgTab = ref<InstanceType<typeof MsgTab> | null>(null);
const activeTab = ref<MessageTabEnum>(MessageTabEnum.UCHAIN_ROLE);
const pagination = new Pagination();
const list = ref<PlatformTypes.IMessageVo[]>([]);
const { menuButtonBounding, menuButtonHeight } = storeToRefs(useAppStore());
const { isLogined, userInfo, activeMerchant } = storeToRefs(useUserStore());
const { queryUnreadCount } = useUserStore();

const scrollViewHeight = computed(
  () =>
    `calc(100vh - 36px - ${menuButtonBounding.value?.height ?? 0}px - ${
      menuButtonBounding.value?.top ?? 0
    }px - ${nutTheme.tabbarHeight} - env(safe-area-inset-bottom))`,
);

const [fetchMsgList, msgState] = useListFetch({
  pagination,
  onListClear: () => {
    list.value = [];
  },
  fetch: async dispatch => {
    const rsp = await platformApi.ApiMessageAppmessagelistPageLimitPost(
      // @ts-ignore
      { userId: userInfo.value?.userId, appType: activeTab.value },
      {
        page: pagination.currentPage,
        limit: pagination.pageSize,
      },
    );
    dispatch(() => {
      pagination.setTotal(rsp.data?.total ?? 0);
      list.value.push(...(rsp.data?.list ?? []));
    });
  },
});

watch(
  [activeTab, isLogined],
  () => {
    if (activeTab.value === MessageTabEnum.FINANCE) return;
    nextTick(async () => {
      await fetchMsgList(FetchStrategy.init);
      await queryReadCount();
      msgTab.value?.tabsUnReadCount();
      queryUnreadCount();
    });
  },
  { immediate: true },
);

watch(
  () => props.bottomTab,
  () => {
    if (props.bottomTab === NavigationTabEnum.Message) {
      nextTick(async () => {
        await fetchMsgList(FetchStrategy.init);
        await queryReadCount();
        msgTab.value?.tabsUnReadCount();
        queryUnreadCount();
      });
      return;
    }
    nextTick(() => queryUnreadCount());
  },
);

async function onRefresh() {
  await fetchMsgList(FetchStrategy.refresh);
  await queryReadCount();
  msgTab.value?.tabsUnReadCount();
  queryUnreadCount();
}

function onLoadMore() {
  fetchMsgList(FetchStrategy.more);
}

const { unknownUrlNavigate } = useNavigate();
async function toDetail(item: PlatformTypes.IMessageVo) {
  if (!item.redirectUrl) return;
  if (
    item?.businessesCode &&
    item?.businessesCode !== activeMerchant.value?.partnerCode
  ) {
    showMsg(`正在切换${item.businessesName}商户`, { useToast: true });
    await delay(1000);
    await userApi.ApiUserSwitchpartnerPost({
      partnerCode: item?.businessesCode ?? "",
    });
  }
  unknownUrlNavigate(item.redirectUrl);
}

async function queryReadCount() {
  await platformApi.ApiMessageUpdateUpdatereadallresultbyapptypePost({
    userId: userInfo.value?.userId,
    readAllResult: true,
    // @ts-ignore
    appType: activeTab.value,
  });
}
</script>
