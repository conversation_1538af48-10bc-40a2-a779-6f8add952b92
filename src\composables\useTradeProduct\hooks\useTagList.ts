import type { MatchTypes } from "@/api";
import type { TRADE_TAG_KEYS, TradeTag } from "@/enums";
import { SupplyType, TRADE_TAG_LABEL, YesOrNo } from "@/enums";

// 获取显示的货主名称
function getPublisherInfo(data: MatchTypes.ProductAppRsp) {
  const isSelf = data.oneself?.code === YesOrNo.yes;

  return isSelf
    ? t("wo-de-fa-bu")
    : data.publishUserInfo
    ? t("datapublishuserinfo-fa-bu", [data.publishUserInfo])
    : "";
}

function isDateTag(tag: string) {
  const targets: TRADE_TAG_KEYS[] = [
    "NEW_GOODS",
    "USED_GOODS",
    "LONG_DATED_GOODS",
    "NEAR_EXPIRY_GOODS",
  ];
  return targets.includes(tag as TRADE_TAG_KEYS);
}

export function useTagList(data: MatchTypes.ProductAppRsp) {
  const publisherInfo = getPublisherInfo(data);
  const showStrictTag = data.supplyType?.code === SupplyType.smartSupply;
  const sourceTagText = data.source?.desc ?? t("jin-kou");

  const formmattedTags: TradeTag[] = (data.tagList ?? []).map(tag => ({
    text: TRADE_TAG_LABEL[tag] ?? "",
    isDateTag: isDateTag(tag),
  }));

  // 内部标签列表(详情页)
  const insideTagTextList: TradeTag[] = [
    ...formmattedTags,
    {
      text: sourceTagText,
      isDateTag: false,
    },
    {
      text: data.saleType?.desc ?? "",
      isDateTag: false,
    },
  ].filter(item => item.text);

  // 外部标签列表(首页列表)
  const outsideTagTextList: string[] = [
    ...formmattedTags.map(t => t.text),
    publisherInfo,
    sourceTagText,
  ];

  const tags = {
    show: outsideTagTextList.length > 0 || showStrictTag,
    outsideTagTextList,
    insideTagTextList,
    showStrictTag,
  };

  return {
    tags,
    insideTagTextList,
    publisherInfo,
  };
}
