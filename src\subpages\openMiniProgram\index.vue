<template>
  <div />
</template>

<script setup lang="ts">
const path = ref("");
const appId = ref("");
const extraToken = ref("");
const { token } = storeToRefs(useUserStore());

async function handleClick() {
  wx.navigateToMiniProgram({
    appId: appId.value,
    path: path.value,
    envVersion: wx.getAccountInfoSync().miniProgram.envVersion,
    extraData: {
      [XHeader.HToken]: extraToken.value || token.value,
    },
    success(res) {
      console.log(JSON.stringify(res));
      uni.reLaunch({
        url: "/pages/index/index",
      });
    },
    fail() {
      uni.navigateBack();
    },
    complete(res) {
      console.log(JSON.stringify(res));
    },
  });
}

onLoad(async query => {
  if (query?.path) {
    path.value = query?.path ?? "";
    appId.value = query?.appId ?? "";
    extraToken.value = query?.extraToken ?? "";
    const localAppId = wx.getAccountInfoSync()?.miniProgram?.appId;
    if (appId.value === "local" || appId.value === localAppId) {
      wx.reLaunch({
        url: path.value,
      });
    } else {
      uni.showModal({
        title: `跳转到${query.title}`,
        confirmText: "跳转",
        success(result) {
          if (result.confirm) {
            handleClick();
          } else {
            uni.navigateBack();
          }
        },
      });
    }
  }
});
</script>
