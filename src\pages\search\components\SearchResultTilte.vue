<template>
  <div class="f-c justify-between">
    <div class="f-c">
      <div
        class="h-[40rpx] w-[10rpx] rounded-[0rpx_6rpx_6rpx_0rpx] bg-blue-trade"
      />
      <div class="ml-20rpx text-32rpx color-black-trade font-600">
        <slot />
      </div>
    </div>

    <div v-if="showMoreBtn" class="f-c pr-12rpx" @click="emit('moreClick')">
      <div class="text-28rpx color-grey">{{ $t("cha-kan-geng-duo") }}</div>
      <!-- @unocss-skip-start -->
      <nut-icon name="rect-right" size="22rpx" />
      <!-- @unocss-skip-end -->
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{ showMoreBtn?: boolean }>();
const emit = defineEmits(["moreClick"]);
</script>
