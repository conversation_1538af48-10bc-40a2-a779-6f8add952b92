<template>
  <div h-100vh flex flex-col items-center bg-white>
    <image mx-auto mb-34rpx mt-110rpx h-102rpx w-102rpx :src="logo" />
    <div class="text-36rpx fw-500 lh-50rpx">{{ title }}</div>
    <div v-if="version" mt-10rpx text-28rpx c-grey lh-40rpx>{{ version }}</div>
    <ul class="mt-94rpx w-702rpx pl-42rpx pr-34rpx">
      <li class="menu-item" @click="goMerchantQualification">
        {{ $t("shang-hu-zi-zhi") }}
      </li>
      <li class="menu-item" @click="goAboutYdt">
        {{ $t("guan-yu-you-ding-te") }}
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { Config } from "@/config";
import { YdtProtocols } from "@/config/protocols";

const version = uni.getAccountInfoSync()?.miniProgram?.version || "";

const logo = "logo.png".toQiniuUrl({ suffixPath: "/assets" });
const title = t("you-ding-te");
const { openWebview } = useNavigate({ webview: { withToken: true } });

function goMerchantQualification() {
  openWebview({
    url: `${Config.merchantUrl}/qualification`,
  });
}

function goAboutYdt() {
  const { url, title } = YdtProtocols.about;
  openWebview({
    url,
    title,
    autoOrigin: true,
  });
}
</script>

<style lang="scss" scoped>
.menu-item {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  height: 100rpx;
  font-size: 30rpx;
  line-height: 42rpx;
  border-bottom: 1rpx solid #f2f2f2;

  &::after {
    display: block;
    width: 48rpx;
    height: 48rpx;
    margin-left: auto;
    content: "";
    background-image: url("@/static/right-arrow.png");
    background-size: cover;
  }
}
</style>
