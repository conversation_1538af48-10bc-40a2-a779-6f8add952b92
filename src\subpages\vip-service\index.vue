<template>
  <div class="relative z-0 h-100vh w-full flex flex-col items-center bg-white">
    <image class="absolute h-1244rpx w-full -z-2" :src="coverImage" />
    <image
      class="absolute top-222rpx h-862rpx w-622rpx -z-1"
      :src="codeBgImage"
    />
    <image mt-142rpx h-160rpx w-160rpx :src="avatarImage" />
    <div class="mt-30rpx text-40rpx c-#fff font-500 lh-56rpx">蒋尚莉</div>
    <image
      class="mt-124rpx"
      size="332rpx"
      :src="codeImage"
      :show-menu-by-longpress="true"
    />

    <div class="mt-36rpx text-26rpx c-#999 lh-36rpx">
      长按识别二维码，添加客服企业微信
    </div>
    <div class="mt-204rpx text-24rpx c-#0591DB">
      此二维码已通过安全验证，可以放心扫码
    </div>
  </div>
</template>

<script setup lang="ts">
const avatarImage = "service-avatar.png".toQiniuUrl();
const coverImage = "service-bg.png".toQiniuUrl();
const codeImage = "service-code.png".toQiniuUrl();
const codeBgImage = "service-bg2.png".toQiniuUrl();
</script>
