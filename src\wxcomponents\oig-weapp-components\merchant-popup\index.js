import { VantComponent } from "../common/component";

VantComponent({
  props: {
    show: {
      type: Boolean,
      value: false,
      observer(val) {
        this.setData({ visible: val });
      },
    },
    list: {
      type: Array,
      value: [],
      observer(val) {
        this.setData({
          merchantList: this.processData(val),
        });
      },
    },
    height: {
      type: String,
      default: "50%",
    },
        showAdd: {
      type: Boolean,
      value: true,
    },
  },
  data: {
    visible: false,
    merchantList: [],
  },

  methods: {
    processData(list) {
      return list.map(item => ({
        ...item,
        authIconClass: this.getAuthIconClass(item.realnameAuthStatus), // 根据状态计算类名
      }));
    },
    getAuthIconClass(status) {
      return (
        {
          PENDING: "icon is-pending",
          SUCCESS: "icon is-success",
        }[status] || "icon"
      );
    },

    handleClose() {
      this.triggerEvent("close");
    },

    handleAddMerchant() {
      this.triggerEvent("add");
    },

    async handleSwitchMerchant(item) {
      try {
        wx.showLoading({ title: "正在切换企业..." });
        this.triggerEvent("switch", { data: item.currentTarget.dataset.item });
        this.handleClose();
      } finally {
        wx.hideLoading();
      }
    },
  },
});
