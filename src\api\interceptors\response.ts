import type { AxiosResponse } from "axios";
import { ResponseCode, XHeader } from "../constant";

export function responseAdapter(res: AxiosResponse) {
  const { setToken } = useUserStore();
  const { headers, status, config } = res;
  const { HToken } = XHeader;

  if (headers?.[HToken]) {
    setToken(headers[HToken]);
  }

  if (config.showLoading) {
    uni.hideLoading();
  }

  if (status === ResponseCode.success && config?.showSuccessMsg) {
    showMsg(res.data.msg || t("cao-zuo-cheng-gong"), {
      duration: 2000,
    });
  }

  if (
    res.data?.rspCode !== ResponseCode.success.toString() &&
    config?.showError
  ) {
    showMsg(res.data.msg || "unknown error", {
      duration: 3000,
    });
  }
  console.log("responseAdapter", res);

  return Promise.resolve(res);
}
