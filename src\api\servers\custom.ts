import type { AxiosInstance } from "axios";
import { adapter } from "@/api/adapter";
import { timeout } from "@/api/constant";
import { errorAdapter } from "@/api/interceptors/error";
import { requestInterceptor } from "@/api/interceptors/request";
import { responseAdapter } from "@/api/interceptors/response";
import axios from "axios";

export interface JsonResult {
  data: any;
  msg: string;
  rspCode: string;
}

class CustomApi {
  request: AxiosInstance;

  constructor() {
    this.request = axios.create({
      adapter,
      timeout,
    });

    this.request.interceptors.request.use(requestInterceptor);
    this.request.interceptors.response.use(responseAdapter, errorAdapter);
  }

  public async fetchUnReadNum(path: string): Promise<JsonResult> {
    return this.request.get(path).then(res => res.data);
  }
}

export const customApi = new CustomApi();
