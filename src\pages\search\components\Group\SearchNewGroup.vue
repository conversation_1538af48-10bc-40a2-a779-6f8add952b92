<template>
  <div class="mb-20px">
    <SearchResultTilte show-more-btn @more-click="toNewsHome">
      {{ $t("zi-xun") }}
    </SearchResultTilte>
    <div class="m-20rpx rounded-card py-10rpx">
      <div
        v-for="item in news"
        :key="item.id"
        class="search-new-item"
        @click="toDetial(item)"
      >
        <view>
          <div class="line-clamp-2 break-all text-30rpx leading-48rpx">
            {{ item.contentTitle }}
          </div>
          <div class="mt-15rpx f-c gap-20rpx text-26rpx color-grey">
            <div v-if="item.contentSource">{{ item.contentSource }}</div>
            <div v-if="item.contentDate">{{ item.contentDate }}</div>
          </div>
        </view>

        <SafeImage
          v-if="item.contentImg"
          custom-class="rounded-8rpx"
          mode="aspectFill"
          width="160rpx"
          height="100rpx"
          :url="item.contentImg.toThumbnailUrl()"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { QueryBackendTypes } from "@/api";
import SearchResultTilte from "../SearchResultTilte.vue";

defineProps<{
  news?: QueryBackendTypes.IArticleVo[];
}>();

const { openWebview } = useNavigate();

function toNewsHome() {
  openWebview({
    url: "/youdingte-news-h5/home",
    autoOrigin: true,
  });
}

function toDetial(item: QueryBackendTypes.IArticleVo) {
  openWebview({
    url: `/youdingte-news-h5/news-detail/${item.id}`,
    autoOrigin: true,
  });
}
</script>

<style lang="scss" scoped>
.search-new-item {
  @apply flex items-start justify-between gap-20rpx mx-40rpx py-26rpx;

  border-bottom: 1px solid theme("colors.grey.light");

  &:last-child {
    border-bottom: none;
  }
}
</style>
