<template>
  <nut-popup
    v-model:visible="showBottom"
    position="bottom"
    round
    :safe-area-inset-bottom="true"
    :custom-style="{ height: '50%' }"
  >
    <div class="box-border h-100% flex flex-col p-30rpx">
      <div class="mb-16rpx flex items-center justify-between">
        <TextMoreBtn :rotate="90" />
        <div
          class="h-42rpx text-30rpx c-#394253 font-400 lh-42rpx"
          @click="handleClear"
        >
          清空
        </div>
      </div>
      <CustomInput
        v-model.trim="inputValue"
        class="h-full min-h-0 flex-1"
        max-height="100%"
        :placeholder="userInputPlaceholder"
      />
      <ChatSend class="mt-16rpx flex justify-end" />
    </div>
  </nut-popup>
</template>

<script setup lang="ts">
import { useChatStore } from "@/store/modules/chat";
import { useSession } from "../composations/useSession";
import ChatSend from "./ChatSend.vue";
import CustomInput from "./CustomInput.vue";
import TextMoreBtn from "./TextMoreBtn.vue";

const showBottom = defineModel<boolean>({ required: true });

const { userInputPlaceholder } = useSession();
const chatStore = useChatStore();
const { inputValue } = storeToRefs(chatStore);

const maxlength = ref<number>(500);
watch(
  () => inputValue.value,
  val => {
    if (val.length > maxlength.value) {
      inputValue.value = val.slice(0, 500);
      showMsg("超过500字, 已自动截断");
    }
  },
);

function handleClear() {
  inputValue.value = "";
}
</script>
