<template>
  <nut-form-item
    custom-class="p-0"
    :label="item.label"
    :prop="item.key"
    :show-error-line="false"
  >
    <slot v-if="$slots.default" />
    <nut-textarea
      v-else-if="item.type === 'textarea'"
      v-model="value"
      :max-length="item.maxLength"
      :placeholder="item.placeholder"
      :disabled="item.disabled"
      :autofocus="item.autofocus"
      limit-show
    />
    <nut-input
      v-else
      v-model="value"
      :placeholder="item.placeholder"
      :type="item.type"
      :max-length="item.maxLength"
      :clearable="item.clearable"
      :autofocus="item.autofocus"
      :disabled="item.disabled"
    />
  </nut-form-item>
</template>

<script setup lang="ts">
defineProps<{ item: CollectFormItem }>();
const value = defineModel<string | undefined>({ required: false });
</script>
