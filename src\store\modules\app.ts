interface MenuButtonBoundingClientRect {
  width: number;
  height: number;
  top: number;
  left: number;
  right: number;
  bottom: number;
}

export const useAppStore = defineStore("app", () => {
  const systemInfo = uni.getSystemInfoSync();
  const screenWidth = ref(systemInfo.screenWidth);
  const screenHeight = ref(systemInfo.screenHeight);

  const statusBarHeight = ref(0);
  const menuButtonHeight = ref(0);
  const menuButtonBounding = ref<MenuButtonBoundingClientRect>();
  const customBarHeight = computed(() =>
    !menuButtonBounding.value
      ? 0
      : menuButtonBounding.value.bottom +
        menuButtonBounding.value.top -
        statusBarHeight.value,
  );

  const calcStatusBarHeight = () => {
    menuButtonBounding.value = uni.getMenuButtonBoundingClientRect();
    statusBarHeight.value = systemInfo?.statusBarHeight || 44;
    menuButtonHeight.value = menuButtonBounding.value?.height || 32;
  };

  return {
    screenWidth,
    screenHeight,
    statusBarHeight,
    customBarHeight,
    menuButtonHeight,
    menuButtonBounding,
    calcStatusBarHeight,
  };
});
