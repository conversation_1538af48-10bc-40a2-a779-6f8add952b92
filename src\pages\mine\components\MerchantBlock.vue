<template>
  <div class="flex items-center justify-between">
    <template v-if="isJoinedMerchant">
      <div class="text-26rpx">{{ merchantLabel }}</div>
      <div
        :class="btnCommonClass"
        class="flex items-center bg-grey3 pl-12rpx pr-20rpx"
        @click="invokeSwitchMerchantPopup"
      >
        <image :src="IconSwitch" class="h-48rpx w-48rpx" />{{
          $t("qie-huan-shang-hu")
        }}
      </div>
    </template>

    <template v-else>
      <div class="text-26rpx color-[#AAB1BE]">
        {{ $t("NotMerchantJoined") }}
      </div>
      <div
        :class="btnCommonClass"
        class="bg-[linear-gradient(114deg,#56CBFF_0%,#008FD6_100%)] px-30rpx py-8rpx color-white"
        @click="handleAddMerchant"
      >
        {{ $t("qu-ru-zhu") }}
      </div>
    </template>
  </div>
  <div
    v-if="
      activeMerchant?.merchantId &&
      activeMerchant?.allowDoAuth &&
      activeMerchant?.showAuth
    "
    class="tips"
  >
    {{ $t("NotMerchantAuthTips") }}
    <span class="ml-8rpx c-#0591DB fw-500" @click="handleMerchantAuth">
      {{ $t("qian-wang-ren-zheng") }} >
    </span>
  </div>
</template>

<script setup lang="ts">
import { Config } from "@/config";
import { MINE_CONTEXT_KEY } from "@/pages/mine/common";
import IconSwitch from "@/static/switch.png";

const userStore = useUserStore();
const { merchantList, activeMerchant, isPersonalEntity } =
  storeToRefs(userStore);

const { openWebview } = useNavigate({ webview: { withToken: true } });
const mineContext = inject(MINE_CONTEXT_KEY);

const btnCommonClass = "text-24rpx lh-34rpx rounded-50rpx";

const isJoinedMerchant = computed(() => {
  return merchantList.value?.length > 0;
});

const merchantLabel = computed(() => {
  return !activeMerchant.value?.merchantId
    ? t("nin-yi-ru-zhu-shang-hu-qing-qie-huan-dao-yi-ru-zhu-shang-hu")
    : `${activeMerchant.value?.realName}（${
        isPersonalEntity.value ? t("ge-ren") : t("qi-ye")
      }）`;
});

function handleAddMerchant() {
  openWebview({
    url: `${Config.merchantUrl}/registration`,
  });
}

function handleMerchantAuth() {
  openWebview({
    url: `${Config.merchantUrl}/auth`,
  });
}

function invokeSwitchMerchantPopup() {
  mineContext?.invokeSwitchMerchant();
}
</script>

<style lang="scss" scoped>
.tips {
  position: relative;
  display: inline-flex;
  padding: 6rpx 24rpx 8rpx 12rpx;
  margin-top: 20rpx;
  font-size: 24rpx;
  line-height: 34rpx;
  color: #878c94;
  background-color: $bgColor;
  border-radius: 8rpx;

  &::before {
    --size: 12rpx;

    position: absolute;
    top: calc(-1 * var(--size));
    left: 12rpx;
    display: block;
    width: 0;
    height: 0;
    content: "";
    border-right: var(--size) solid transparent;
    border-bottom: var(--size) solid $bgColor;
    border-left: var(--size) solid transparent;
  }
}
</style>
