<template>
  <div
    class="relative mt-72rpx rounded-card rounded-[24rpx]"
    :class="{ 'bg-op-0': isVip }"
  >
    <div
      class="flex items-center bg-[length:100%_100%] bg-no-repeat px-20rpx py-26rpx"
      :class="{
        'pt-30rpx pb-50rpx': isVip,
      }"
      :style="{
        backgroundImage: isVip
          ? `url(${userInfo?.memberTypeInfo?.memberCardUrl})`
          : ``,
      }"
    >
      <div :class="isVip ? vipAvatarClass : ''" @click="toPerson">
        <image
          :src="avatar"
          mode="scaleToFill"
          class="size-100rpx circle"
          :class="isVip && avatar ? vipImageClass : ''"
        />
      </div>
      <div class="ml-26rpx">
        <div class="f-c">
          <div
            class="text-36rpx font-600"
            :style="{
              color: isVip ? userInfo?.memberTypeInfo?.memberColor : '#394253',
            }"
          >
            {{ userInfo?.showName }}
          </div>
          <RealNameTag />
        </div>
        <div v-if="isVip" class="mt-6rpx">
          <div class="text-22rpx text-#84B1E4">
            {{ userInfo?.memberTypeInfo?.name }}会员到期时间：{{
              userInfo?.memberEndDate
            }}
          </div>
        </div>
      </div>
      <div v-if="isVip" class="absolute right-34rpx top--54rpx">
        <image
          :src="userInfo?.memberTypeInfo?.memberIconUrl"
          mode="scaleToFill"
          class="size-176rpx"
        />
      </div>
    </div>

    <div class="mt--20rpx rounded-card px-20rpx py-22rpx">
      <MerchantBlock />

      <div v-if="activeMerchant" class="mt-36rpx flex justify-around">
        <EntityItem
          v-for="item in merchantEntity"
          :key="item.name"
          :badge="item.unread"
          :icon="item.icon"
          :name="item.name"
          @click="() => handleMerchantItemClick(item)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { AnyFn } from "@vueuse/core";
import { Config } from "@/config";
import AvatarDefault from "@/static/avatar-default.png";
import AvatarVip from "@/static/avatar-vip.png";
import CouponIcon from "@/static/coupon-icon.png";
import Shzl from "@/static/shzl.png";
import Wddq from "@/static/wddq.png";
import Yggl from "@/static/yggl.png";
import Yqcy from "@/static/yqcy.png";
import ZiliaoIcon from "@/static/ziliao-icon.png";
import Zjzh from "@/static/zjzh.png";
import MerchantBlock from "./MerchantBlock.vue";
import RealNameTag from "./RealNameTag.vue";

interface MerchantEntryItem {
  name: string;
  icon: string;
  unread?: number;
  hidden?: boolean;
  onclick?: AnyFn;
  url?: string;
}

const userStore = useUserStore();
const {
  userInfo,
  activeMerchant,
  isPersonalEntity,
  hasPayPermission,
  fundAccountUrl,
} = storeToRefs(userStore);
const { openWebview } = useNavigate({ webview: { withToken: true } });

const vipBg = "vip.png".toQiniuUrl();
const vipAvatarClass =
  "f-c-c border-1 border-solid border-[#DEEEFB] rounded-full";
const vipImageClass = "border-2  border-white";

const isVip = computed(() => userInfo.value?.memberFlag);

const avatarVip = computed(() => {
  return isVip.value ? AvatarVip : AvatarDefault;
});

const avatar = computed(() => userInfo.value?.avatar ?? avatarVip.value);

const basicEntity = ref([
  {
    name: "个人资料",
    icon: ZiliaoIcon,
    onclick: () => {
      uni.navigateTo({
        url: "/pages/profile/index",
      });
    },
  },
  {
    name: "优惠券",
    icon: CouponIcon,
    onclick: () => {
      openWebview({ url: `${Config.baseUrl}/coupon-h5/pages/index` });
    },
  },
]);

const merchantEntity: ComputedRef<MerchantEntryItem[]> = computed(() => {
  return [
    {
      name: "员工管理",
      icon: Yggl,
      unread: 0,
      url: `${Config.merchantUrl}/staff`,
    },
    {
      name: "我的电签",
      icon: Wddq,
      url: `${Config.merchantUrl}/e-contract`,
    },
    {
      name: "邀请成员",
      icon: Yqcy,
      onclick: () => {
        uni.navigateTo({ url: "/subpages/invite/index" });
      },
    },

    {
      name: "资金账户",
      icon: Zjzh,
      hidden: !hasPayPermission.value || isPersonalEntity.value,
      onclick: async () => {
        await userStore.queryFundAccountUrl();
        fundAccountUrl.value && openWebview({ url: fundAccountUrl.value });
      },
    },
    {
      name: "商户资料",
      icon: Shzl,
      url: `${Config.merchantUrl}/info`,
    },
  ].filter(item => !item.hidden);
});

async function handleMerchantItemClick({ url, onclick }: MerchantEntryItem) {
  if (typeof onclick === "function") {
    await onclick();
  }
  if (url) {
    openWebview({ url });
  }
}

function toPerson() {
  uni.navigateTo({
    url: "/pages/profile/index",
  });
}
</script>
