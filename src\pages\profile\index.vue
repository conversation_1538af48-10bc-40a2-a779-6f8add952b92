<template>
  <nut-cell-group>
    <nut-cell :title="$t('tou-xiang')" center>
      <template #desc>
        <image :src="userInfo?.avatar" class="size-90rpx circle" />
      </template>
    </nut-cell>
    <nut-cell
      :title="$t('ni-cheng')"
      is-link
      @click="openDialog('nickName', $t('ni-cheng'))"
    >
      <template #desc>
        {{ userInfo?.nickName ?? "--" }}
      </template>
    </nut-cell>
  </nut-cell-group>

  <nut-cell-group>
    <nut-cell
      :title="$t('you-xiang')"
      is-link
      @click="openDialog('email', $t('you-xiang'))"
    >
      <template #desc>
        {{ userInfo?.email ?? "--" }}
      </template>
    </nut-cell>
    <nut-cell :title="$t('shou-ji-hao')">
      <template #desc>
        {{ userInfo?.mobileNum ? maskPhoneNumber(userInfo.mobileNum) : "--" }}
      </template>
    </nut-cell>
    <nut-cell :title="$t('shi-ming-ren-zheng')" is-link @click="toRealNamePage">
      <template #desc>
        <div class="f-c justify-end">
          {{ userInfo?.showName ?? "--" }}
          <RealNameTag readonly />
        </div>
      </template>
    </nut-cell>
  </nut-cell-group>

  <nut-dialog
    v-if="showDialog"
    v-model:visible="showDialog"
    :pop-style="{ top: '25%' }"
    :title="dialogTitle"
    :close-on-click-overlay="false"
    :ok-auto-close="false"
    @ok="confirmUpdate"
  >
    <div class="dialog-form">
      <nut-form ref="ruleForm" :rules="rules" :model-value="formData">
        <FormItem v-model="formData[action]" :item="formConfig[action]" />
      </nut-form>
    </div>
  </nut-dialog>
</template>

<script setup lang="ts">
import type { FormInst } from "nutui-uniapp";
import { maskPhoneNumber } from "@/utils/shared";
import RealNameTag from "../mine/components/RealNameTag.vue";

defineOptions({
  options: { styleIsolation: "shared" },
});

type DialogAction = "nickName" | "email";

useTitle(t("ge-ren-zi-liao"));
const ruleForm = ref<FormInst | null>(null);
const userStore = useUserStore();
const { formData, rules, formConfig } = useForm({
  nickName: {
    rules: [FormRules.required()],
    maxLength: 20,
    autofocus: true,
  },
  email: {
    autofocus: true,
    rules: [FormRules.required(), FormRules.isEmail()],
  },
});
const { userInfo } = storeToRefs(userStore);
const { toRealNamePage } = useNavigate();

const action = ref<DialogAction>("nickName");
const dialogTitle = ref("");
const showDialog = ref(false);

function openDialog(type: DialogAction, title: string) {
  formData.email = userInfo.value?.email;
  formData.nickName = userInfo.value?.nickName;

  action.value = type;
  showDialog.value = true;
  dialogTitle.value = `修改${title}`;
}

function confirmUpdate() {
  ruleForm.value?.validate().then(async ({ valid }) => {
    if (valid) {
      showDialog.value = false;
      if (action.value === "nickName") {
        await updateNickName();
      } else if (action.value === "email") {
        await updateEmail();
      }
      userStore.queryUserInfo();
    }
  });
}

async function updateEmail() {
  await userApi.ApiUserEditemailPost(
    {
      email: formData.email,
    },
    { showLoading: true, showSuccessMsg: true },
  );
}

async function updateNickName() {
  await userApi.ApiUserUpdatenicknamePost(
    {
      nickName: formData.nickName,
    },
    { showLoading: true, showSuccessMsg: true },
  );
}
</script>

<style lang="scss" scoped>
.dialog-form {
  ::v-deep .nut-cell {
    padding: 15rpx 10rpx;
    background: theme("colors.light");
  }
}
</style>
