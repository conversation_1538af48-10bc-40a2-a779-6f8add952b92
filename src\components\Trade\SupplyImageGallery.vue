<template>
  <nut-swiper
    height="auto"
    :pagination-visible="gallery.length > 1"
    :pagination-color="unotheme.colors.primary"
  >
    <nut-swiper-item v-for="(item, index) in gallery" :key="index">
      <div class="relative" @click="!isDefaultImage(item) && preview(item)">
        <image mode="aspectFill" class="w-100%" :src="getImageUrl(item)" />
        <div
          v-if="isDefaultImage(item)"
          class="absolute bottom-20rpx left-20rpx w-90% text-32rpx color-white"
        >
          <div v-for="title in item.titles" :key="title" class="text-ellipsis">
            {{ title }}
          </div>
        </div>
      </div>
    </nut-swiper-item>
  </nut-swiper>
</template>

<script setup lang="ts">
import type { MatchTypes } from "@/api";
import { unotheme } from "@/styles/themes/uno.theme";

const { data } = defineProps<{
  data: MatchTypes.SupplyProductAppRsp;
}>();
const { gallery } = useSupplyImage(data);
const urls = gallery.map(getImageUrl);

type ImageItem = (typeof gallery)[number];

// 获取图片请求路径
function getImageUrl(item: ImageItem) {
  return typeof item === "string"
    ? item.toThumbnailUrl()
    : item.url?.toThumbnailUrl();
}

// 是否为缺省图对象
function isDefaultImage(item: ImageItem): item is SupplyThumbnail {
  return typeof item === "object" && item.titles.length > 0;
}

// 预览大图
function preview(item: ImageItem) {
  wx.previewImage({
    item: getImageUrl(item),
    urls,
  });
}
</script>
