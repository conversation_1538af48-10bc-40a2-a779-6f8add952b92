<template>
  <nut-cell-group>
    <nut-cell
      v-for="item in protocols"
      :key="item.title"
      :title="item.title"
      is-link
      @click="
        openWebview({ url: item.url, title: item.title, autoOrigin: true })
      "
    />
  </nut-cell-group>
</template>

<script setup lang="ts">
import { YdtProtocols } from "@/config/protocols";

useTitle(t("fu-wu-xie-yi"));

const protocols = [YdtProtocols.register, YdtProtocols.privacy];
const { openWebview } = useNavigate();
</script>
