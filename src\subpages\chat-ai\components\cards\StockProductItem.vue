<template>
  <view class="box" @click="emit('click')">
    <view class="flex flex-wrap text-[24rpx] text-[#878c94] fw-400">
      <view
        v-for="(item, index) in dataList"
        :key="index"
        class="mb-20rpx w-25%"
        :style="{ 'text-align': item.align }"
      >
        <view class="label">{{ item.label }}</view>
        <view
          class="mt-6rpx break-words text-[#333]"
          :class="{ 'text-[#fd5555]': item?.red }"
        >
          {{ item.value }}
        </view>
      </view>
    </view>
    <view class="line" style="margin-top: 0" />

    <view class="goods-name">
      {{ `${data.countryName} | ${data.factoryCode} | ${data.goodsName}` }}
    </view>

    <view class="detail">
      <view v-for="item in detailList" :key="item.label" class="item">
        <view class="label">{{ item.label }}</view>
        <view class="content">{{ item.value || "--" }}</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import type { AgentCustomerTypes } from "@/api";

interface List {
  label: string;
  value: string | undefined | number;
  align: "left" | "right" | "center";
  red?: boolean;
}

const props = withDefaults(
  defineProps<{ data: AgentCustomerTypes.IStockVo }>(),
  {
    data: () => ({}),
  },
);

const emit = defineEmits(["picking", "click"]);

const dataList = computed((): List[] => {
  return [
    {
      label: "件数",
      value: props.data.pieceNum,
      align: "left",
    },
    {
      label: "净重(KG)",
      value: props.data.stockWeight,
      align: "center",
    },
    {
      label: "单价",
      value: props.data.price,
      align: "center",
    },
    {
      label: "金额(美金)",
      value: props.data.amount,
      align: "right",
    },
  ];
});

const detailList = computed((): List[] => {
  return [
    {
      label: "产品要素",
      value: props.data.declared,
      align: "left",
    },
    {
      label: "生产日期",
      value: `${props.data.prodEndDate || ""}至${
        props.data.prodStartDate || ""
      }`,
      align: "left",
    },
    {
      label: "卫生证号",
      value: props.data.healthCertificate,
      align: "left",
    },
  ];
});
</script>

<style lang="scss" scoped>
.box {
  padding: 28rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0rpx 0rpx 40rpx 0rpx rgb(216 216 216 / 50%);

  .top {
    display: flex;
    justify-content: space-between;
    margin-bottom: 34rpx;

    .right {
      display: flex;
      align-items: center;
      width: 136rpx;
      height: 52rpx;
      padding: 6rpx;
      font-size: 26rpx;
      font-weight: 400;
      line-height: 52rpx;
      color: #1478f0;
      border: 2rpx solid #deefff;
      border-radius: 32rpx;

      .add {
        width: 40rpx;
        height: 40rpx;
        margin-right: 8rpx;
        line-height: 40rpx;
        text-align: center;
        background: #deefff;
        border-radius: 50%;
      }
    }
  }

  .line {
    width: 100%;
    height: 2rpx;
    margin: 24rpx 0;
    background-image: linear-gradient(to right, #b8b9ba 50%, white 50%);
    background-size: 24rpx 2rpx;
  }

  .goods-name {
    display: -webkit-box;
    overflow: hidden;
    font-size: 30rpx;
    font-weight: 600;
    line-height: 42rpx;
    color: #33373f;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2; /* 显示的行数 */
    word-break: break-all;
    -webkit-box-orient: vertical; /* 垂直方向上的盒子 */
  }

  .detail {
    padding: 20rpx;
    margin-top: 22rpx;
    background: #f6f8fc;
    border-radius: 16rpx;

    .item {
      display: flex;
      padding: 10rpx 0;
      font-size: 26rpx;
      font-weight: 400;

      .label {
        width: 200rpx;
        color: #878c94;
      }

      .content {
        flex: 1;
        color: #33373f;
      }
    }
  }
}
</style>
