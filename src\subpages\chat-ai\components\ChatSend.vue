<template>
  <image
    :src="curSendIcon"
    class="block h-64rpx w-64rpx"
    @click="handleSubmit"
  />
</template>

<script setup lang="ts">
import SendActiveIcon from "@/static/chat/send-active-icon.png";
import SendFocusIcon from "@/static/chat/send-focus-icon.png";
import SendIcon from "@/static/chat/send-icon.png";
import { useChatStore } from "@/store/modules/chat";
import { useSession } from "../composations/useSession";

const { createConversation, queryMessage, userInputPlaceholder, source } =
  useSession();

const chatStore = useChatStore();
const { loading, inputValue, sessionId } = storeToRefs(chatStore);

const userStore = useUserStore();
const { userInfo } = storeToRefs(userStore);

// 发送图标
const curSendIcon = computed(() => {
  if (loading.value.chatTyping) {
    return SendActiveIcon;
  }
  if (inputValue.value.length > 0) {
    return SendFocusIcon;
  }
  return SendIcon;
});

async function handleSubmit() {
  if (inputValue.value.length === 0) {
    return uni.showToast({ title: userInputPlaceholder.value, icon: "none" });
  }
  if (loading.value.chatTyping) {
    return source.value?.cancel("手动取消请求");
  }

  // 创建会话
  if (!sessionId.value) {
    createConversation();
  }
  queryMessage({
    user_id: userInfo.value?.userId || "",
    prompt: inputValue.value,
  });

  inputValue.value = "";
}
</script>
