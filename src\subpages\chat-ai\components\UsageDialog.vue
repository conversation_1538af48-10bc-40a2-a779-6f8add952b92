<template>
  <nut-dialog
    ref="dialogRef"
    transition="zoom"
    custom-class="-mt-20rpx pb-12rpx! rounded-24rpx!"
  >
    <div flex flex-col items-start text-left>
      <div class="text-48rpx c-black font-bold lh-66rpx">
        {{ systemUsage.title }}
      </div>
      <div class="mt-16rpx text-28rpx lh-40rpx">{{ systemUsage.content }}</div>
      <div class="tip mt-32rpx">
        今日使用上限
        <label>{{ systemUsage.total }}</label>
        次
      </div>
      <div class="tip mt-8rpx">
        已使用
        <label>{{ systemUsage.used }}</label>
        次
      </div>
      <nut-button
        custom-class="mt-96rpx! h-96rpx! w-full! rounded-96rpx! bg-[linear-gradient(180deg,#00bded_0%,#0093dc_100%)]! c-white! border-none!"
        @click="handleClose"
      >
        我知道了
      </nut-button>
    </div>
  </nut-dialog>
</template>

<script setup lang="ts">
import type { DialogInst } from "nutui-uniapp";
import { useChatStore } from "@/store/modules/chat";

const { systemUsage } = storeToRefs(useChatStore());
const dialogRef = ref<DialogInst>();

function open() {
  dialogRef.value?.showDialog({
    noFooter: true,
  });
}

function handleClose() {
  dialogRef.value?.onCancel();
}

defineExpose({
  open,
});
</script>

<style lang="scss" scoped>
.tip {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #93a1be;

  label {
    margin: 0 8rpx;
    font-weight: 500;
    color: $uni-text-color !important;
  }

  &::before {
    display: block;
    width: 10rpx;
    height: 10rpx;
    margin-right: 12rpx;
    content: "";
    background-color: #f66;
    border-radius: 50%;
  }
}
</style>
