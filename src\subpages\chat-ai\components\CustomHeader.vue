<template>
  <div
    class="fixed left-0 top-0 z-10 box-border w-full bg-white"
    :style="{
      paddingTop: `${menuButtonBounding?.top}px`,
      height: `${customBarHeight}px`,
    }"
  >
    <div
      class="relative flex items-center w-full!"
      :style="{
        height: `${menuButtonHeight}px`,
      }"
    >
      <image
        class="absolute ml-24rpx h-44rpx w-44rpx"
        :src="ArrowLeftIcon"
        @click="handleBack"
      />
      <div class="mx-auto text-17px fw-500">小优</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import ArrowLeftIcon from "@/static/arrow-left.svg";

const { customBarHeight, menuButtonBounding, menuButtonHeight } = storeToRefs(
  useAppStore(),
);

function handleBack() {
  const pages = getCurrentPages();
  if (pages.length >= 2) {
    uni.navigateBack();
  } else {
    uni.reLaunch({
      url: "/pages/index/index",
    });
  }
}
</script>
