<template>
  <nut-cell-group>
    <nut-cell
      v-for="item in menuItems"
      :key="item.title"
      :title="item.title"
      is-link
      :to="item.to"
    />
  </nut-cell-group>

  <div class="bottom-cell">
    <nut-cell
      :title="$t('tui-chu-deng-lu')"
      is-link
      @click="showLogoutSheet = true"
    />
    <nut-safe-area position="bottom" />
  </div>

  <nut-action-sheet
    v-model:visible="showLogoutSheet"
    :cancel-txt="$t('qu-xiao')"
    :description="$t('qing-que-ren-shi-fou-tui-chu-zhang-hao')"
    :menu-items="sheetItems"
    @choose="handleLogout"
  />
</template>

<script setup lang="ts">
import type { ActionSheetOption } from "nutui-uniapp";
import { delay } from "oig-utils";

useTitle(t("wo-de-she-zhi"));
const { logout } = useUserStore();
const showLogoutSheet = ref(false);
const { generatorWebviewUrl } = useNavigate({
  webview: { withToken: true, autoOrigin: true },
});

const menuItems = ref([
  // {
  //   title: t("she-zhi-yu-yan"),
  // },
  {
    title: t("yi-jian-fan-kui"),
    to: generatorWebviewUrl({
      url: `/help-center-h5/feedback?title=${t("yi-jian-fan-kui")}`,
    }),
  },
  {
    title: t("fu-wu-xie-yi"),
    to: "/subpages/protocolList/index",
  },
  {
    title: t("xiu-gai-mi-ma"),
    to: "/subpages/password/reset",
  },
  {
    title: t("guan-yu-wo-men-0"),
    to: "/subpages/about/index",
  },
]);

const sheetItems = ref<ActionSheetOption[]>([
  {
    name: t("que-ren-tui-chu"),
    color: unotheme.colors.red.DEFAULT,
  },
]);

async function handleLogout(item: ActionSheetOption, index: number) {
  if (index === 0) {
    uni.showLoading({ title: t("tui-chu-zhong") });
    await logout({ manually: true });
    await delay(1000);
    uni.hideLoading();
    useNavigate().redirectToIndex();
  }
}
</script>

<style lang="scss" scoped>
.bottom-cell {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;

  ::v-deep .nut-cell {
    margin: 0;

    .title {
      text-align: center;
    }

    .nut-cell__link {
      display: none;
    }
  }
}
</style>
