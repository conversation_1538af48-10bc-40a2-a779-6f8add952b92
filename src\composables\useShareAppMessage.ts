interface ExtraOptions extends Page.CustomShareContent {
  useCurrentPath?: boolean;
}

export const defaultShareOptions = {
  title: "优顶特 | 生鲜冻品产业平台",
  path: "/pages/index/index",
};

export function useShareAppMessage(
  options: Page.ShareAppMessageOption,
  extraOptions: ExtraOptions = { useCurrentPath: false },
) {
  const { getCurrentRoute } = useNavigate();
  const currentRoute = getCurrentRoute();
  const { $page } = currentRoute;

  const customOptions: Page.CustomShareContent = {
    ...defaultShareOptions,
    ...extraOptions,
  };

  if (extraOptions.useCurrentPath && $page?.fullPath) {
    customOptions.path = $page.fullPath;
  }

  return customOptions;
}
