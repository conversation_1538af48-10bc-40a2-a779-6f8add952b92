export enum TradeProductType {
  /** 求购 */
  purchase = "PURCHASE",
  /** 供应 */
  supply = "SUPPLY",
}

export enum TradeSaleType {
  /** 现货 */
  spot = "SPOT",
  /** 预售 */
  presale = "PRESALE",
  /** 半预售 */
  halfPresale = "HALF_PRESALE",
}

export enum YesOrNo {
  yes = "Y",
  no = "N",
}

export enum GridAppType {
  /** 站内webview */
  webview = "H5",
  /** 小程序 */
  miniProgram = "MINI_PROGRAM",
}

export enum GridFunctionType {
  outside = "OUTSIDE",
  inside = "INSIDE",
}

export enum GridAppPosition {
  /** 市场 */
  store = "STORE",
  /** 代理 */
  agent = "AGENT",
  /** 代理工具 */
  agentTool = "AGENT_TOOL",
  /** 代理信息 */
  agentMessage = "AGENT_MESSAGE",
}

export enum LoginWay {
  /** 验证码登录 */
  sms,
  /** 密码登录 */
  password,
}

export enum RealNameAuth {
  /** 实名中 */
  PENDING = "PENDING",
  /** 已实名 */
  SUCCESS = "SUCCESS",
  /** 未实名 */
  UN_AUTH = "UN_AUTH",
  FAILED = "FAILED",
  REVOKED = "REVOKED",
}

export enum MerchantEntityTypeEnum {
  /** 个人 */
  PERSONAL = "PERSONAL",
  /** 个体工商户 */
  SELF_EMPLOYED = "SELF_EMPLOYED",
  /** 公司 */
  COMPANY = "COMPANY",
}

export enum RoleEnum {
  /** 代理 */
  UCHAIN = "UCHAIN_ROLE",
  /** 支付 */
  PAY = "PAY_ROLE",
}

export enum SupplyType {
  /** 自主报盘 */
  autonomySupply = "AUTONOMY_SUPPLY",
  /** 半自主报盘 */
  halfAutonomySupply = "HALF_AUTONOMY_SUPPLY",
  /** 自动报盘 */
  smartSupply = "SMART_SUPPLY",
}

export enum ProductSourceType {
  /** 国产 */
  DOMESTIC_GOODS = "DOMESTIC_GOODS",
  /** 进口 */
  IMPORT_GOODS = "IMPORT_GOODS",
}

export enum MessageTabEnum {
  /** 代理 */
  UCHAIN_ROLE = "UCHAIN_ROLE",
  /** 官方 */
  OFFICIAL = "OFFICIAL",
  /** 金融 */
  FINANCE = "FINANCE",
}

export enum MessageTypeEnum {
  /** 个人 */
  PERSONAL = "PERSONAL",
  /** 商户 */
  MERCHANT = "MERCHANT",
}

export enum ServiceTypeEnum {
  /** 撮合 */
  MATCHMAKING = "P01",
  /** 一键火卖家 */
  SHOP_SELLER = "P02",
  /** 运营 */
  OPERATION = "P03",
  /** 一键火买家 */
  SHOP_BUYER = "P04",
  /** 代理 */
  AGENT = "P07",
}

export enum ProxyOrderStatusEnum {
  /** 未开始 */
  A00 = "A00",
  /** 未到港 */
  B00 = "B00",
  /** 清关中 */
  C10 = "C10",
  /** 清关完成 */
  C20 = "C20",
  /** 已入库 */
  C30 = "C30",
}

export enum RealNameTypeEnum {
  /** 个人 */
  USER = "USER",
  /** 商户 */
  MERCHANT = "MERCHANT",
}

export enum MerchantCategoryEnum {
  DomesticCompany = "Z002", // 国内公司
  DomesticIndividual = "Z004", // 国内个人
  ForeignCompany = "Z003", // 国外公司
  ForeignIndividual = "Z005", // 国外个人
}

export enum CardTypeEnum {
  orderCard = "orderCard", // 查订单卡片
  clearanceCard = "clearanceCard", // 查清关进度
  shipTimeCard = "shipTimeCard", // 查船期
  stockGoodsCard = "stockGoodsCard", // 查库存卡片
  FILE = "FILE", // 查附件
}
