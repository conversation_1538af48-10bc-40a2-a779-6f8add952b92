<template>
  <div
    class="fixed left-0 z-1 h-auto w-full"
    :style="{ top: `${customBarHeight}px` }"
  >
    <div
      class="flex justify-between bg-white px-30rpx pb-26rpx pt-16rpx text-28rpx c-#33373F"
    >
      <div class="flex items-center" @click="handleClick">
        <image
          :src="HistoryListIcon"
          mode="scaleToFill"
          class="mr-12rpx block h-48rpx w-48rpx"
        />
        <div class="font-400 lh-40rpx">历史对话</div>
      </div>
      <div class="flex items-center" @click="handleCreateChat">
        <image
          :src="NewChatIcon"
          mode="scaleToFill"
          class="mr-12rpx block h-48rpx w-48rpx"
        />
        <div class="font-400 lh-40rpx">新建对话</div>
      </div>
      <div class="flex items-center" @click="invokeSwitchMerchantPopup">
        <image :src="ChatSwitchIcon" class="mr-12rpx block h-48rpx w-48rpx" />
        <div class="font-400 lh-40rpx">切换商户</div>
      </div>
    </div>
    <!-- 历史对话 -->
    <ChatHistory v-model="showHistory" />
    <!-- 切换商户 -->
    <SwitchMerchant v-model="merchantPopupVisible" />
  </div>
</template>

<script setup lang="ts">
import SwitchMerchant from "@/components/Merchant/SwitchMerchant.vue";
import ChatSwitchIcon from "@/static/chat/chat-switch.png";
import HistoryListIcon from "@/static/chat/history-list.png";
import NewChatIcon from "@/static/chat/new-chat.png";
import { useSession } from "../composations/useSession";
import ChatHistory from "./ChatHistory.vue";

defineOptions({
  options: { styleIsolation: "shared" },
});

const { customBarHeight } = storeToRefs(useAppStore());
const { createConversation } = useSession();

// 历史对话
const showHistory = ref<boolean>(false);
function handleClick() {
  showHistory.value = true;
}

// 新建对话
function handleCreateChat() {
  createConversation();
}

// 切换商户
const merchantPopupVisible = ref<boolean>(false);
function invokeSwitchMerchantPopup() {
  merchantPopupVisible.value = true;
}
</script>

<style>
.nut-list-item {
  width: 100%;
  height: auto !important;
  margin: 0;
}
</style>
