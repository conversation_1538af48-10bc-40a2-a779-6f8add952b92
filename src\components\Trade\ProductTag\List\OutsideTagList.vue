<template>
  <div class="flex flex-wrap gap-12rpx">
    <FeatureTag v-for="tag in tags.outsideTagTextList" :key="tag">
      {{ tag }}
    </FeatureTag>
    <SmartTag v-if="tags.showStrictTag" />
  </div>
</template>

<script setup lang="ts">
import FeatureTag from "@/components/Trade/ProductTag/FeatureTag.vue";
import SmartTag from "../SmartTag.vue";

defineProps<{
  tags: ReturnType<typeof useTradeProduct>["tags"];
}>();
</script>
