export type TRADE_TAG_KEYS = keyof typeof source;

export interface TradeTag {
  text: string;
  isDateTag: boolean;
}

const source = {
  SPECIAL_PRICE: "特价",
  URGENT_SALE: "急售",
  DETACHABLE_CABINET: "可拆柜",
  NEW_GOODS: "新货",
  USED_GOODS: "旧货",
  LONG_DATED_GOODS: "大日期货",
  NEAR_EXPIRY_GOODS: "临期货",
};

export const TRADE_TAG_LABEL: {
  [key in TRADE_TAG_KEYS | string]: string;
} = {
  ...source,
};
