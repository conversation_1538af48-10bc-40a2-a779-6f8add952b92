<template>
  <uni-shadow-root class="oig-weapp-components-merchant-popup-index"
    ><van-popup
      :show="visible"
      position="bottom"
      :round="true"
      :close-on-click-overlay="true"
      :safe-area-inset-bottom="false"
      @close="handleClose"
    >
      <view class="head-box">
        <view class="title">我的商户</view>
        <view class="add-btn" @click="handleAddMerchant">
          <image class="add-btn-icon" src="../static/add-o.png"></image>
          添加商户
        </view>
      </view>
      <view class="list-box">
        <block v-for="(item, index) in merchantList" :key="item.index">
          <view
            class="merchant-item"
            :data-item="item"
            @click="handleSwitchMerchant"
          >
            <view class="content-box">
              <view class="row">
                <view :class="'icon ' + item.authIconClass"></view>
                <view class="name">{{ item.realName }}</view>
              </view>
              <block v-if="item.openService">
                <view class="row">
                  <block
                    v-for="(item, index) in item.openService"
                    :key="item.serviceId"
                  >
                    <view class="label">{{ item.serviceName }}</view>
                  </block>
                </view>
              </block>
              <block v-else>
                <view class="row">
                  <view class="tips">暂未开通业务</view>
                </view>
              </block>
            </view>
            <image
              v-if="item.currentActive"
              class="active-icon"
              src="../static/check-red.png"
            ></image>
          </view>
        </block>
      </view> </van-popup
  ></uni-shadow-root>
</template>

<script>
import VanPopup from "../popup/index.vue";
global["__wxVueOptions"] = { components: { "van-popup": VanPopup } };

global["__wxRoute"] = "oig-weapp-components/merchant-popup/index";
import { VantComponent } from "../common/component";

VantComponent({
  props: {
    show: {
      type: Boolean,
      value: false,
      observer(val) {
        this.setData({ visible: val });
      },
    },
    list: {
      type: Array,
      value: [],
      observer(val) {
        this.setData({
          merchantList: this.processData(val),
        });
      },
    },
  },
  data: {
    visible: false,
    merchantList: [],
  },

  methods: {
    processData(list) {
      return list.map(item => ({
        ...item,
        authIconClass: this.getAuthIconClass(item.realnameAuthStatus), // 根据状态计算类名
      }));
    },
    getAuthIconClass(status) {
      return (
        {
          PENDING: "icon is-pending",
          SUCCESS: "icon is-success",
        }[status] || "icon"
      );
    },

    handleClose() {
      this.triggerEvent("close");
    },

    handleAddMerchant() {
      this.triggerEvent("add");
    },

    async handleSwitchMerchant(item) {
      try {
        wx.showLoading({ title: "正在切换企业..." });
        this.triggerEvent("switch", { data: item.currentTarget.dataset.item });
        this.handleClose();
      } finally {
        wx.hideLoading();
      }
    },
  },
});
export default global["__wxComponents"][
  "oig-weapp-components/merchant-popup/index"
];
</script>
<style platform="mp-weixin">
.head-box {
  --blue: #26b9f7;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 13px 13px 22px;
  box-shadow: 0px 1px 10px 1px rgba(0, 0, 0, 0.1);
}

.head-box .title {
  font-size: 18px;
  font-weight: 500;
  line-height: 25px;
}

.add-btn {
  display: flex;
  align-items: center;
  row-gap: 1px;
  padding: 3px 9px 3px 9px;
  border-radius: 12px;
  border: 1px solid var(--blue);
  font-size: 13px;
  line-height: 18px;
  font-weight: 500;
  color: var(--blue);
}

.add-btn-icon {
  margin-top: -2px;
  width: 16px;
  height: 16px;
}

.list-box {
  display: flex;
  flex-direction: column;
  padding: 12px 13px 24px 12px;
  box-sizing: border-box;
  max-height: calc(50vh - 60px);
  overflow-y: auto;
}

.merchant-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14px 14px 15px 16px;
  margin-bottom: 6px;
  background-color: #f5f7f8;
  border-radius: 6px;
}

.content-box {
  display: flex;
  flex-direction: column;
  flex-basis: 295px;
  flex-grow: 0;
  overflow: hidden;
  row-gap: 1px;
}

.row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 2px;
}

.row .name {
  font-size: 14px;
  line-height: 21px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.row .icon {
  margin-top: -1px;
  flex-shrink: 0;
  width: 55px;
  height: 21px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-image: url("http://qiniu-test.worldrou.com/assets/not-authenticated.png");
}

.row .icon.is-pending {
  background-image: url("http://qiniu-test.worldrou.com/assets/authenticating.png");
}

.row .icon.is-success {
  background-image: url("http://qiniu-test.worldrou.com/assets/authenticated.png");
}

.row .code {
  font-size: 13px;
  color: #7486a7;
  line-height: 18px;
}

.row .label {
  margin-top: 5px;
  margin-right: 4px;
  padding: 0 4px;
  font-size: 10px;
  border-radius: 1px;
  border: 1px solid #26b9f7;
  color: #26b9f7;
  line-height: 12px;
}

.row .tips {
  font-size: 13px;
  line-height: 18px;
  color: #7486a7;
}

.active-icon {
  width: 24px;
  height: 24px;
}
</style>
