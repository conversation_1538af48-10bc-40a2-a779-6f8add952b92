<template>
  <div class="py-100rpx text-center">
    <div v-if="loading" class="mb-12rpx">
      <nut-icon
        name="loading1"
        :size="loadingIconSize"
        :custom-color="unotheme.colors.grey.DEFAULT"
      />
    </div>
    <image v-else class="size-320rpx" :src="img" mode="scaleToFill" />

    <div class="text-28rpx color-grey">
      {{ loading ? $t("jia-zai-zhong") : $t("zan-wu-shu-ju") }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { unotheme } from "@/styles/themes/uno.theme";

defineProps<{ loading?: boolean }>();
const img = "no-data.png".toQiniuUrl();
const loadingIconSize = "26px";
</script>
