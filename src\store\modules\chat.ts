import type * as ChatTypes from "@/api/servers/chat/type";
import { isTempId } from "@/utils/is";
import { uniqueId } from "@/utils/shared";
import dayjs from "dayjs";

export const useChatStore = defineStore("chat", () => {
  const sessionId = ref<string>(""); // 当前会话ID
  const messages = ref<ChatTypes.ChatMessage[]>([]);
  const conversations = ref<ChatTypes.Sessions[]>([]);
  const systemDefaultData = ref<ChatTypes.SystemDefaultDataType>({});
  const xyTips = ref<ChatTypes.GetXyTipsReq>({});
  const systemUsage = ref<ChatTypes.SystemUsageType>({});
  const loading = ref({
    /** 页面整体加载中 */
    pageLoading: false,
    /** 是否正在输出回答中 */
    chatTyping: false,
    /** 是否正在请求语音转文字中 */
    translating: false,
    /** 是否正在上传图片中 */
    userInputImageLoading: false,
  });
  const showMoreText = ref<boolean>(false); // 是否显示更多文本弹框
  const inputValue = ref(""); // 输入框内容

  const setShowMoreText = () => {
    showMoreText.value = !showMoreText.value;
  };

  const setSessionId = (val: string) => {
    sessionId.value = val;
  };

  const setSystemDefaultData = (val: ChatTypes.SystemDefaultDataType) => {
    systemDefaultData.value = val;
  };

  const clearMessages = () => {
    messages.value = [];
  };

  const initLoading = () => {
    loading.value = {
      pageLoading: false,
      chatTyping: false,
      translating: false,
      userInputImageLoading: false,
    };
  };

  const updateMessage = (id: string, message: ChatTypes.updateChatMessage) => {
    console.log("updateMessage", id, message);

    const index = messages.value.findIndex(
      (message: ChatTypes.ChatMessage) => message.id === id,
    );
    if (index !== -1) {
      console.log("更新消息", messages.value[index], message);
      messages.value[index] = { ...messages.value[index], ...message };
    }
    return messages.value[index];
  };

  // 添加消息
  const addMessage = (message: ChatTypes.addChatMessage) => {
    const item = {
      id: uniqueId(),
      date: dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
      content: "",
      ...message,
    };
    messages.value.push(item);
    return item;
  };

  // 发送消息接口成功后，真正创建会话
  const confirmConversation = (conversationId: string) => {
    if (isTempId(sessionId.value)) {
      setSessionId(conversationId);
    }
  };

  const getMessageById = (id: string) => {
    return messages.value.find(message => message.id === id);
  };

  return {
    loading,
    conversations,
    inputValue,
    messages,
    xyTips,
    systemUsage,
    clearMessages,
    getMessageById,
    sessionId,
    setSessionId,
    systemDefaultData,
    setSystemDefaultData,
    showMoreText,
    setShowMoreText,
    confirmConversation,
    initLoading,
    updateMessage,
    addMessage,
  };
});
