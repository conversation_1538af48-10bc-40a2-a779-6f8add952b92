<template>
  <div
    class="mb-16rpx flex justify-between rounded-16rpx bg-primary px-32rpx py-24rpx text-[white] shadow-[inset_2rpx_2rpx_0rpx_0rpx_rgba(255,255,255,0.2)]"
  >
    <div
      v-for="(item, index) in list"
      :key="index"
      class="text-align-center"
      @click="skip(item)"
    >
      <div class="mt-10rpx">
        <span class="text-48rpx lh-66rpx">{{ item.value }}</span>
        <span v-if="index < 2" class="ml-6rpx text-26rpx">{{ $t("gui") }}</span>
      </div>
      <div class="text-26rpx">{{ item.name }}</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
interface ItemType {
  name: string;
  value: string | number;
  url: string;
}

const { openWebview } = useNavigate({
  webview: { withToken: true, autoOrigin: true },
});
const { isLogined } = storeToRefs(useUserStore());

const list = ref<ItemType[]>([
  {
    name: t("ding-dan"),
    value: 0,
    url: "/proxy-h5/pages/order/index",
  },
  {
    name: t("ku-cun"),
    value: 0,
    url: "/proxy-h5/pages/stock/index",
  },
  {
    name: t("jin-rong-ti-kuan"),
    value: "-",
    url: "/bop-scfp-h5/withdrawals/list?activeTab=ALL",
  },
  {
    name: t("shou-xin-chan-pin"),
    value: "-",
    url: "/bop-scfp-h5/credit/areadyCredit",
  },
]);

watchEffect(getData);

function getData() {
  if (isLogined.value) {
    getOrder();
    getStock();
    getLoaned();
    getCredit();
  }
}

// 获取订单数量
async function getOrder() {
  const res = await agentCustomerApi.ApiOrderGetordercountbystatusGet();
  list.value[0].value = res?.data || 0;
}
// 获取库存数量
async function getStock() {
  const res = await agentCustomerApi.ApiOrderGetorderstockcountbystatusGet();
  list.value[1].value = res?.data || 0;
}
// 获取金融提款数量
async function getLoaned() {
  const res = await agentCustomerApi.ApiScfpCustomerloanedcountPost();
  list.value[2].value = res?.data || 0;
}
// 获取授信产品数量
async function getCredit() {
  const res =
    await agentCustomerApi.ApiScfpCustomercreditfinanceproductcountPost();
  list.value[3].value = res?.data || 0;
}

function skip(item: ItemType) {
  if (item.value === "-") {
    uni.showToast({
      title: t("poryx.total"),
      icon: "none",
    });
    return;
  }

  openWebview({ title: item.name, url: item.url });
}

defineExpose({
  getData,
});
</script>
