import type { ICustomerCouponStatistical } from "youxian-api/src/youdingteCoupons/types";
import { first } from "lodash";

/**
 * 优惠券统计数据处理
 * @param couponStatistical 优惠券统计数据
 */
export function useCouponsStatistical(couponStatistical?: MaybeRef<ICustomerCouponStatistical | undefined>) {
  const couponSumList = computed(() => unref(couponStatistical)?.couponSumList ?? []);
  const quarterOrderNum = computed(() => unref(couponStatistical)?.quarterOrderNum ?? 0);
  const hasGroupCode = computed(() => unref(couponStatistical)?.groupCode !== undefined);
  const groupQtOdNum = computed(() => unref(couponStatistical)?.groupQtOdNum ?? 0);
  const progressCabinetNum = computed(() => {
    if (unref(hasGroupCode)) {
      return unref(groupQtOdNum);
    }
    return unref(quarterOrderNum);
  });

  /** 优惠列表为空 */
  const isCouponSumListEmpty = computed(() => couponSumList.value.length === 0);

  /** 需要渲染的柜量门槛值区间 */
  const couponsRange = computed(() => {
    // 对数据按num升序排序
    const sortedData = [...unref(couponSumList)].sort((a, b) => (a?.cabinetThreshold ?? 0) - (b?.cabinetThreshold ?? 0));

    for (let index = 0; index < sortedData.length; index++) {
      const { cabinetThreshold = 0 } = sortedData[index];
      if (unref(progressCabinetNum) <= cabinetThreshold) {
        return [sortedData[index], sortedData[index + 1]];
      }

      // index 等于倒数第三个时
      if (index === sortedData.length - 3) {
        if (unref(progressCabinetNum) <= cabinetThreshold) {
          return [sortedData[index], sortedData[index + 1]];
        }
        if (unref(progressCabinetNum) > cabinetThreshold && unref(progressCabinetNum) < (sortedData[index + 1]?.cabinetThreshold ?? 0)) {
          return [sortedData[index], sortedData[index + 1]];
        }
      }

      // index 等于倒数第二个时
      if (index === sortedData.length - 2) {
        return [sortedData[index], sortedData[index + 1]];
      }
    }
  });

  /** 最大柜量门槛值 */
  const maxCabinetThreshold = computed(() => {
    return couponSumList.value.reduce((max, item) => {
      return max > (item?.cabinetThreshold ?? 0) ? max : (item?.cabinetThreshold ?? 0);
    }, 0);
  });

  /** 是否最后一个柜量门槛区间 */
  const lastCouponsRange = computed(() => {
    return (couponsRange.value?.[1]?.cabinetThreshold ?? 0) >= unref(maxCabinetThreshold);
  });

  /** 是否超过或等于最大柜量门槛值 */
  const isExceedMaxCabinetThreshold = computed(() => {
    return unref(progressCabinetNum) >= unref(maxCabinetThreshold) && !unref(isCouponSumListEmpty);
  });

  /** 下个优惠柜量 */
  const _nextCouponThreshold = computed(() => {
    const first = couponsRange.value?.[0]?.cabinetThreshold ?? 0;
    const second = couponsRange.value?.[1]?.cabinetThreshold ?? 0;
    if (progressCabinetNum.value < first) {
      return first;
    }

    return second;
  });

  /** 距离下个优惠的柜量 */
  const distanceNextCouponThreshold = computed(() => {
    return unref(_nextCouponThreshold) - unref(progressCabinetNum);
  });

  /** 计算距离下个优惠每柜可优惠的金额 */
  const distanceNextCouponAmount = computed(() => {
    const sourceList = unref(couponSumList);
    const nextThreshold = unref(_nextCouponThreshold);

    if (!sourceList?.length || nextThreshold <= 0) return 0;

    const sortedData = [...sourceList].sort(
      ({ cabinetThreshold: a = 0 }, { cabinetThreshold: b = 0 }) => a - b,
    );
    const targetIndex = sortedData.findIndex(
      ({ cabinetThreshold = 0 }) => cabinetThreshold === nextThreshold,
    );

    if (targetIndex === -1) return 0;
    let totalAmount = 0;
    if (targetIndex === 0) {
      totalAmount = [first(sortedData)!]?.reduce((sum, { couponAmount = 0, number = 0 }) =>
        sum + Number(couponAmount ?? 0) * number,
        0);
    } else {
      totalAmount = sortedData
        .slice(0, targetIndex + 1)
        .reduce((sum, { couponAmount = 0, number = 0 }) =>
          sum + Number(couponAmount ?? 0) * number,
          0);
    }

    return (totalAmount / nextThreshold).toFixed(0);
  });

  /** 所有优惠金额 */
  const couponSumAmount = computed(() => {
    return couponSumList.value.reduce((sum, item) => {
      return sum + (Number(item.couponAmount ?? 0)) * (item?.number ?? 0);
    }, 0);
  });

  return {
    /** 优惠列表为空 */
    isCouponSumListEmpty,
    /** 需要渲染的柜量门槛值区间 */
    couponsRange,
    /** 最大柜量门槛值 */
    maxCabinetThreshold,
    /** 是否超过或等于最大柜量门槛值 */
    isExceedMaxCabinetThreshold,
    /** 是否最后一个柜量门槛区间 */
    lastCouponsRange,
    /** 距离下个优惠的柜量 */
    distanceNextCouponThreshold,
    /** 季度订单数量 */
    quarterOrderNum,
    /** 距离下个优惠每柜可优惠的金额 */
    distanceNextCouponAmount,
    /** 所有优惠金额 */
    couponSumAmount,
    /** 是否有集团编码 */
    hasGroupCode,
    /** 集团季度订单总数量 */
    groupQtOdNum,
    /** 用于计算进度条柜数 */
    progressCabinetNum,
  };
}
