/*
 * 处理小程序在i18n中不能传入变量格式化的问题，需强制转换函数
 * https://ask.dcloud.net.cn/question/143367
 */
export function convertI18nSlot<T extends Record<string, any>>(dict: T) {
  const dictResult: Record<string, any> = {};

  for (const dictKey in dict) {
    const data = dict[dictKey];
    const result: Record<string, any> = {};
    for (const key in data) {
      const value = data[key];

      if (/\{\d+\}/.test(value)) {
        // @ts-ignore
        result[key] = ({ list }) =>
          value.replace(/\{(\d+)\}/g, (_: any, p1: string) =>
            list(Number.parseInt(p1)),
          );
      } else {
        result[key] = value;
      }
    }

    dictResult[dictKey] = result;
  }

  return dictResult as T;
}
