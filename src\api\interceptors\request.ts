import type { AxiosRequestConfig } from "axios";
import { XHeader } from "@/api/constant";
import { AuthConfig, Config } from "@/config";

export function requestInterceptor(config: AxiosRequestConfig) {
  const { token, isLogined } = useUserStore();
  if (!config.baseURL && config.url?.startsWith("/demo/")) {
    config.url = Config.chatBaseUrl + config.url;
  } else {
    config.url = Config.baseUrl + (config.baseURL ?? "") + config.url;
  }

  config.headers[XHeader.xClientId] = AuthConfig.clientId;
  config.headers[XHeader.xChannelCode] = AuthConfig.channelId;
  config.headers[XHeader.xAppId] = AuthConfig.appId;
  config.headers[XHeader.xDeviceOs] = AuthConfig.runtime;
  config.headers[XHeader.xProductCode] = AuthConfig.productCode;

  if (isLogined) {
    config.headers[XHeader.HToken] = token;
  }

  if (config.showLoading) {
    uni.showLoading({
      title: t("jia-zai-zhong"),
      mask: true,
    });
  }

  return config;
}
