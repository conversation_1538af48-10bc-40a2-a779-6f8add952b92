<template>
  <div
    class="mb-20rpx flex flex-col rounded-[16rpx] bg-white px-30rpx"
    @click="emit('click')"
  >
    <div
      class="h-86rpx flex items-center border-b-2rpx border-b-#ebebeb border-b-solid"
    >
      <image
        :src="data.dictDetailRemark ?? DefaultIcon"
        mode="scaleToFill"
        class="mr-14rpx size-48rpx rounded-[50%]"
      />
      <div class="f-c flex flex-1 text-28rpx text-#394253">
        {{ data.dictDetailName }}
        <image
          v-if="data.messageType === MessageTypeEnum.MERCHANT"
          src="@/static/msg_merchant.png"
          mode="scaleToFill"
          class="ml-8rpx h-38rpx w-126rpx"
        />
      </div>
      <div class="text-24rpx text-#999999">{{ data.noticeTime }}</div>
    </div>
    <div class="flex-1">
      <div class="mt-22rpx text-32rpx text-#394253 font-600">
        {{ data.title }}
      </div>
      <div class="pb-28rpx pt-14rpx text-26rpx">
        <div class="line-clamp-4">{{ data.content }}</div>
      </div>
    </div>
    <div
      v-if="data.redirectUrl?.startsWith('http')"
      class="h-90rpx f-c justify-between border-t-2rpx border-t-#ebebeb border-t-solid text-28rpx text-#1193db"
    >
      查看详情
      <image
        src="@/static/right-arrow.png"
        mode="aspectFill"
        class="size-48rpx"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import type { PlatformTypes } from "@/api";
import { MessageTypeEnum } from "@/enums";
import DefaultIcon from "@/static/esign-icon.png";

defineProps<{
  data: PlatformTypes.IMessageVo;
}>();
const emit = defineEmits(["click"]);
</script>

<style scoped></style>
