<template>
  <view class="box" @click="emit('click')">
    <view class="mb-34rpx flex items-center justify-between">
      <view class="h-36rpx text-26rpx c-#33373F font-400 lh-36rpx">
        {{ data.contract }} / {{ data.cabinetNo }}
      </view>
      <view class="flex items-center justify-center gap-x-8rpx">
        <!-- <view :class="curLeftLabel.className">{{ curRightLabel.label }}</view> -->
        <view :class="curRightLabel.calssName">{{ curRightLabel.label }}</view>
      </view>
    </view>
    <view class="flex flex-wrap text-[24rpx] text-[#878c94] fw-400">
      <view
        v-for="(item, index) in dataList"
        :key="index"
        class="mb-24rpx w-33.3%"
        :style="{ 'text-align': item.align }"
      >
        <view class="label">{{ item.label }}</view>
        <view
          class="mt-6rpx break-words text-[#333]"
          :class="{ 'text-[#fd5555]': item?.red }"
        >
          {{ item.value }}
        </view>
      </view>
    </view>
    <view class="line" style="margin-top: 0" />

    <view class="goods-name">
      {{ `${data.countryName} | ${data.factoryCode} | ${data.goodsName}` }}
    </view>
  </view>
</template>

<script lang="ts" setup>
import type { AgentCustomerTypes } from "@/api";
import { ClearanceStatusEnum, CustomsInsFlag } from "@/enums/chat";

interface List {
  label: string;
  value: string | undefined | number;
  align: "left" | "right" | "center";
  red?: boolean;
}

interface IIStockVo extends AgentCustomerTypes.IStockVo {
  customsInsFlag?: string;
  clearanceStatus?: string;
  allAmount?: string;
  finalVoyageEta?: string;
  portName?: string;
}

const props = withDefaults(defineProps<{ data: IIStockVo }>(), {
  data: () => ({}),
});

const emit = defineEmits(["click"]);

const dataList = computed((): List[] => {
  return [
    {
      label: "合同金额(美金)",
      value: handleData(props.data.allAmount),
      align: "left",
    },
    {
      label: "ETA",
      value: handleData(props.data.finalVoyageEta),
      align: "center",
    },
    {
      label: "入境地",
      value: handleData(props.data.portName),
      align: "right",
    },
  ];
});

function handleData(data?: string) {
  return data || "-";
}

// 清关状态
const O_ClearanceStatusEnum = {
  [ClearanceStatusEnum.ALL]: {
    label: "全部",
    calssName: "disable-label",
  },
  [ClearanceStatusEnum.CLEARANCE]: {
    label: "清关中",
    calssName: "disable-label",
  },
  [ClearanceStatusEnum.FINISH]: {
    label: "已放行",
    calssName: "plain-blue-label",
  },
};

// 查验标记
const O_CustomsInsFlag = {
  [CustomsInsFlag.NO]: {
    label: "不查验",
    className: "green-label",
  },
  [CustomsInsFlag.YES]: {
    label: "查验",
    className: "green-label",
  },
  [CustomsInsFlag.WAIT]: {
    label: "带确定",
    className: "green-label",
  },
};

const curLeftLabel = computed(() => {
  return O_CustomsInsFlag[props.data.customsInsFlag as CustomsInsFlag];
});

const curRightLabel = computed(() => {
  return O_ClearanceStatusEnum[
    props.data.clearanceStatus as ClearanceStatusEnum
  ];
});
</script>

<style lang="scss" scoped>
.box {
  padding: 28rpx 24rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0rpx 0rpx 40rpx 0rpx rgb(216 216 216 / 50%);

  .line {
    width: 100%;
    height: 2rpx;
    margin: 24rpx 0;
    background-image: linear-gradient(to right, #b8b9ba 50%, white 50%);
    background-size: 24rpx 2rpx;
  }

  .goods-name {
    display: -webkit-box;
    overflow: hidden;
    font-size: 30rpx;
    font-weight: 600;
    line-height: 42rpx;
    color: #33373f;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2; /* 显示的行数 */
    word-break: break-all;
    -webkit-box-orient: vertical; /* 垂直方向上的盒子 */
  }
}
</style>
