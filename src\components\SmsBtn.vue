<template>
  <nut-button
    :custom-style="{ padding: `0 6rpx` }"
    shape="square"
    size="small"
    custom-color="#000000"
    :disabled="counting || loading"
    block
    @click="trigger"
  >
    {{ currentText }}
  </nut-button>
</template>

<script setup lang="ts">
import type { UserTypes } from "@/api";
import { isPhoneStr } from "oig-utils";

const props = withDefaults(
  defineProps<{
    countdown?: number;
    onTrigger?: () => void;
    payload?: UserTypes.ISendSmsCodeDto;
  }>(),
  {
    countdown: 30,
  },
);

const current = ref(props.countdown);
const loading = ref(false);
const counting = ref(false);
const currentText = computed(() => {
  if (counting.value) {
    return t("currentvalues-hou-zhong-fa", [current.value]);
  }
  return t("fa-song-yan-zheng-ma");
});

async function trigger() {
  if (counting.value) {
    return;
  }

  if (props.payload) {
    const { phoneNumber } = props.payload;
    if (!phoneNumber) {
      showMsg(t("qing-shu-ru-shou-ji-hao"));
      return;
    }
    if (!isPhoneStr(phoneNumber)) {
      showMsg(t("shou-ji-ge-shi-bu-zheng-que"));
      return;
    }

    await sendSmsCode(props.payload);
  }

  props.onTrigger?.();
  startCountdown();
}

function startCountdown() {
  counting.value = true;
  current.value = props.countdown;
  const timer = setInterval(() => {
    current.value--;
    if (current.value <= 0) {
      clearInterval(timer);
      counting.value = false;
    }
  }, 1000);
}

async function sendSmsCode(payload: UserTypes.ISendSmsCodeDto) {
  loading.value = true;
  try {
    await userApi.ApiSmsSendsmscodePost(payload, { showSuccessMsg: true });
  } finally {
    loading.value = false;
  }
}
</script>
