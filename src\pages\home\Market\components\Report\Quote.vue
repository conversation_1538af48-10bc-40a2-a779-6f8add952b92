<template>
  <div
    class="relative rounded-8rpx px-7rpx py-15rpx text-25rpx font-500"
    :style="{ background: quoteTrend.background }"
    @click="toDetailPage"
  >
    <div
      class="text-ellipsis text-center color-black-bold"
      :style="{ fontSize }"
    >
      {{ title }}
    </div>
    <div
      class="h-43rpx text-center text-30rpx font-600 line-height-42rpx"
      :style="{ color: quoteTrend.color }"
    >
      {{ data?.avgPrice?.toString() ?? "--" }}
    </div>
    <div
      class="f-c-c text-center text-24rpx"
      :style="{ color: quoteTrend.color }"
    >
      <image :src="quoteTrend.image" class="size-36rpx" />
      {{ quoteTrend.toRatePercent() }}
    </div>
  </div>
</template>

<script setup lang="ts">
import type { PlatformTypes } from "@/api";

const props = defineProps<{ data: PlatformTypes.IPriceSituationAvgH5Vo }>();
const { openWebview } = useNavigate();
const { data } = toRefs(props);

const title = computed(
  () => `${data.value.countryName ?? ""}${data.value.productName ?? ""}`,
);
const quoteTrend = computed(() =>
  useQuoteTrend(data.value.riseFallRate, data.value.riseFallValue),
);

const fontSize = computed(() => {
  return title.value.length >= 14
    ? "16rpx"
    : title.value.length >= 10
    ? "20rpx"
    : "24rpx";
});

function toDetailPage() {
  openWebview({
    url: `/youdingte-news-h5/price-detail/${data.value.id}`,
    autoOrigin: true,
  });
}
</script>
