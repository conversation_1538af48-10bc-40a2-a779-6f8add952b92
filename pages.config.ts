import { defineUniPages } from "@uni-helper/vite-plugin-uni-pages";

export default defineUniPages({
  pages: [
    {
      path: "pages/index/index",
      layout: "default",
      style: {
        navigationStyle: "custom",
        navigationBarTextStyle: "black",
      },
    },
    {
      path: "pages/webview/index",
    },
    {
      path: "pages/webview/previewDocument",
    },
    {
      path: "pages/login/index",
    },
    {
      path: "pages/login/tel/index",
    },
    {
      path: "pages/setting/index",
    },
    {
      path: "pages/profile/index",
    },
    {
      path: "pages/search/history",
    },
    {
      path: "pages/search/index",
    },
  ],
  subPackages: [
    {
      root: "subpages",
      pages: [
        {
          path: "feedback/index",
        },
        {
          path: "password/forget",
        },
        {
          path: "password/reset",
        },
        {
          path: "protocolList/index",
        },
        {
          path: "customer-service/index",
        },
        {
          path: "invite/index",
        },
        {
          path: "trade/purchase",
        },
        {
          path: "trade/supply",
        },
        {
          path: "realname/form",
        },
        {
          path: "realname/info",
        },
        {
          path: "chat-ai/index",
          style: {
            navigationStyle: "custom",
            navigationBarTextStyle: "black",
            navigationBarTitleText: "小优",
            disableScroll: true,
          },
        },

        {
          path: "share/index",
        },
        {
          path: "share/success",
        },
        {
          path: "about/index",
        },
        {
          path: "news/newsDetail",
          style: {
            navigationBarTitleText: "资讯详情",
          },
        },
        {
          path: "news/reportDetail",
          style: {
            navigationBarTitleText: "研报详情",
          },
        },
        {
          path: "openMiniProgram/index",
          style: {
            navigationBarTitleText: "跳转中...",
          },
        },
        {
          path: "vip-service/index",
          style: {
            navigationBarTitleText: "会员服务",
          },
        },
      ],
    },
    {
      root: "merchant",
      pages: [
        {
          path: "registration",
        },
        {
          path: "switch",
        },
      ],
    },
  ],
  globalStyle: {
    navigationBarTextStyle: "black",
    navigationBarBackgroundColor: "#FFFFFF",
    usingComponents: {
      "merchant-popup":
        "wxcomponents/oig-weapp-components/merchant-popup/index",
    },
  },
});
