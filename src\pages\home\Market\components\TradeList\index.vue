<template>
  <div class="rounded">
    <nut-sticky :offset-top="-5">
      <nut-tabs v-model="tab" align="left">
        <nut-tab-pane
          v-for="item in tabs"
          :key="item.value"
          :pane-key="item.value"
          :title="item.title"
        />
      </nut-tabs>
    </nut-sticky>
    <PurchaseList
      v-show="tab === TradeProductType.purchase"
      ref="purchaseList"
    />
    <SupplyList v-show="tab === TradeProductType.supply" ref="supplyList" />
  </div>
</template>

<script setup lang="ts">
import { TradeProductType } from "@/enums";
import PurchaseList from "./PurchaseList.vue";
import SupplyList from "./SupplyList.vue";

defineOptions({
  options: { styleIsolation: "shared" },
});
defineExpose({ dispatchFetch });

const { isLogined } = storeToRefs(useUserStore());
const purchaseList = ref<InstanceType<typeof PurchaseList> | null>(null);
const supplyList = ref<InstanceType<typeof SupplyList> | null>(null);

const tabs = ref([
  {
    title: t("qiu-gou"),
    value: TradeProductType.purchase,
  },
  {
    title: t("gong-ying"),
    value: TradeProductType.supply,
  },
]);

const tab = ref(TradeProductType.purchase);

function dispatchFetch(strategy: FetchStrategy) {
  if (tab.value === TradeProductType.purchase) {
    purchaseList.value?.fetchPurchaseList(strategy);
  } else {
    supplyList.value?.fetchSupplyList(strategy);
  }
}

watch([tab, isLogined], () => {
  nextTick(() => dispatchFetch(FetchStrategy.init));
});

onBeforeMount(() => {
  dispatchFetch(FetchStrategy.init);
});
</script>

<style lang="scss" scoped>
view {
  --nut-tabs-horizontal-titles-item-active-line-width: 36rpx;
  --nut-tabs-horizontal-titles-item-min-width: 260rpx;
  --nut-tab-pane-padding: 0px;
  --nut-tabs-titles-item-font-size: 30rpx;
}

::v-deep .nut-tabs {
  border-radius: 16rpx;
}

::v-deep .nut-tabs__titles-left {
  display: flex;
  justify-content: center;
  border-radius: 16rpx;
}

::v-deep .nut-tabs__titles {
  border-bottom: 2px solid #fbfbfb;
}
</style>
