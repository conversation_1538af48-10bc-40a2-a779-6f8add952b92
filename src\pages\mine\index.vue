<template>
  <BackgroundLayout :background="background">
    <TopMenuBar class="ml-22rpx f-c">
      <image
        v-for="item in TopItems"
        :key="item.icon"
        :src="item.icon"
        class="mr-10rpx size-66rpx"
        @click="item.onClick"
      />
    </TopMenuBar>
    <scroll-view
      scroll-y
      :style="{ height: scrollViewHeight }"
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <div class="mx-16rpx">
        <Personal />
        <Proxy v-if="hasUchainPermission" />
        <MoreServe />
      </div>
    </scroll-view>
    <SwitchMerchant v-model="merchantPopupVisible" />
  </BackgroundLayout>
</template>

<script setup lang="ts">
import SwitchMerchant from "@/components/Merchant/SwitchMerchant.vue";
import QAIcon from "@/static/qa-icon.png";
import SettingIcon from "@/static/setting-icon.png";
import { delay } from "oig-utils";
import { MINE_CONTEXT_KEY } from "./common";
import MoreServe from "./components/MoreServe.vue";
import Personal from "./components/Personal.vue";
import Proxy from "./components/Proxy.vue";

const { runQueryTask } = useEntry();
const { openWebview } = useNavigate({ webview: { withToken: true } });
const { hasUchainPermission, userInfo } = storeToRefs(useUserStore());
const { menuButtonBounding } = storeToRefs(useAppStore());
const refreshing = ref(false);

const merchantPopupVisible = ref(false);

provide(MINE_CONTEXT_KEY, {
  invokeSwitchMerchant: () => {
    merchantPopupVisible.value = true;
  },
});

const background = computed(() => {
  return userInfo.value?.memberFlag
    ? userInfo.value?.memberTypeInfo?.memberMapUrl ?? ""
    : "MINE";
});

const scrollViewHeight = computed(
  () =>
    `calc(100vh  - ${menuButtonBounding.value?.height ?? 0}px - ${
      menuButtonBounding.value?.top ?? 0
    }px - ${nutTheme.tabbarHeight} - env(safe-area-inset-bottom))`,
);

const isVip = computed(() => userInfo.value?.memberFlag);

const TopItems = [
  {
    icon: QAIcon,
    onClick: () => {
      openWebview({ url: "/help-center-h5", autoOrigin: true });
    },
  },
  {
    icon: SettingIcon,
    onClick: () => {
      uni.navigateTo({
        url: "/pages/setting/index",
      });
    },
  },
];

async function onRefresh() {
  refreshing.value = true;
  try {
    await runQueryTask();
  } finally {
    await delay(500);
    refreshing.value = false;
  }
}
</script>
