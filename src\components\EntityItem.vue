<template>
  <div class="f-c flex-col" @click="emit('click')">
    <nut-badge :value="badge" :hidden="!badge">
      <image :src="icon" class="size-56rpx" />
    </nut-badge>
    <div
      v-if="num !== undefined"
      class="h-44rpx text-32rpx color-black-bold font-500 line-height-44rpx"
    >
      {{ num }}
    </div>
    <div class="text-24rpx color-black-bold">{{ name }}</div>
  </div>
</template>

<script setup lang="ts">
defineProps<{ icon: string; name: string; badge?: number; num?: number }>();
const emit = defineEmits(["click"]);
</script>
