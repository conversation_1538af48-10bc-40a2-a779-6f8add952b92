<template>
  <TitleCard title="更多服务">
    <div
      class="flex justify-around py-16rpx"
      :style="{ width: `${entity.length * 20}%` }"
    >
      <EntityItem
        v-for="item in entity"
        :key="item.name"
        :icon="item.icon"
        :name="item.name"
        @click="item.callback"
      />
    </div>
  </TitleCard>
</template>

<script setup lang="ts">
import Bzzx from "@/static/bzzx.png";
import UserInvite from "@/static/user-invite.png";
import Yjfk from "@/static/yjfk.png";

const { openWebview } = useNavigate({ webview: { withToken: true } });

const entity = ref([
  {
    name: t("bang-zhu-zhong-xin"),
    icon: Bzzx,
    callback: () => {
      openWebview({ url: "/help-center-h5", autoOrigin: true });
    },
  },
  {
    name: t("yi-jian-fan-kui"),
    icon: Yjfk,
    callback: () => {
      openWebview({
        url: `/help-center-h5/feedback?title=${t("yi-jian-fan-kui")}`,
        autoOrigin: true,
      });
    },
  },
  {
    name: t("yong-hu-yao-qing"),
    icon: UserInvite,
    callback: () => {
      openWebview({
        url: "/merchant-h5/user-registration/invite",
        autoOrigin: true,
      });
    },
  },
]);
</script>
