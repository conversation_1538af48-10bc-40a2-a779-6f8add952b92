// 查询列表策略
export enum FetchStrategy {
  init, // 初始化列表
  refresh, // 刷新列表
  more, // 加载更多数据
}

type ListFetch = (strategy: FetchStrategy) => void;
export type LoadMoreStatus = "more" | "loading" | "noMore";
export interface ListFetchState {
  loading: boolean;
  loadMoreStatus: LoadMoreStatus;
  fetching: boolean;
  refreshing: boolean;
}

interface FetchListOption {
  pagination: Pagination;
  onListClear: () => void;
  fetch: (dispatch: (callback: () => void) => void) => Promise<any>;
}

export function useListFetch({
  pagination,
  onListClear,
  fetch,
}: FetchListOption): [ListFetch, ListFetchState] {
  const state = reactive<ListFetchState>({
    loading: false,
    fetching: false,
    refreshing: false,
    loadMoreStatus: "noMore",
  });

  const doFetch: ListFetch = async strategy => {
    if (state.fetching) {
      return;
    }

    let shouldShowLoading = false; // 是否应该显示loading
    let shouldResetCurrentPage = false; // 是否应该将页码重置到第一页
    let shouldClearList = false; // 是否应该查询前清除列表
    let shouldShowRefreshing = false; // 是否触发下拉刷新状态

    if (strategy === FetchStrategy.init) {
      shouldShowLoading = true;
      shouldResetCurrentPage = true;
      shouldClearList = true;
    } else if (strategy === FetchStrategy.refresh) {
      shouldShowLoading = false;
      shouldResetCurrentPage = true;
      shouldClearList = true;
      shouldShowRefreshing = true;
    } else if (strategy === FetchStrategy.more) {
      shouldShowLoading = false;
      shouldResetCurrentPage = false;
      shouldClearList = false;
    }

    if (shouldShowLoading) {
      state.loading = true;
    }

    if (shouldResetCurrentPage) {
      pagination?.setCurrentPage(1);
    }

    if (strategy === FetchStrategy.more) {
      if (pagination?.isLastPage) {
        state.loadMoreStatus = "noMore";
        return;
      } else {
        state.loadMoreStatus = "loading";
        pagination?.next();
      }
    }

    if (strategy === FetchStrategy.init && shouldClearList) {
      onListClear?.();
    }

    if (shouldShowRefreshing) {
      state.refreshing = true;
    }

    try {
      state.fetching = true;
      await fetch(callback => {
        if (shouldClearList) {
          onListClear?.();
        }
        callback();
      });
    } finally {
      state.fetching = false;

      nextTick(() => {
        state.loadMoreStatus = pagination.isLastPage ? "noMore" : "more";
      });

      if (shouldShowRefreshing) {
        state.refreshing = false;
      }

      if (shouldShowLoading) {
        state.loading = false;
      }
    }
  };

  return [doFetch, state];
}
