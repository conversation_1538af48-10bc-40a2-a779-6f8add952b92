<template>
  <div
    class="h-70rpx w-70rpx flex items-center justify-center rounded-full bg-#fff shadow-[0rpx_0rpx_16rpx_0rpx_rgba(0,0,0,0.21)]"
    @click="backBottom"
  >
    <image
      :src="BottomArrowIcon"
      mode="scaleToFill"
      class="block h-38rpx w-38rpx"
    />
  </div>
</template>

<script setup lang="ts">
import BottomArrowIcon from "@/static/chat/bottom-arrow.png";

const emit = defineEmits<{
  (e: "back-bottom"): void;
}>();

function backBottom() {
  emit("back-bottom");
}
</script>
