{"name": "you<PERSON><PERSON>-weapp", "version": "0.0.1", "packageManager": "pnpm@10.6.2", "license": "MIT", "engines": {"node": "20"}, "scripts": {"api": "node upgrade.api.js", "dev": "npm run dev:mp-weixin", "build": "npm run build:mp-weixin", "dev:mp-weixin": "cross-env VITE_MODE=development uni -p mp-weixin", "dev:chat": "cross-env VITE_BUILD_MODE=chat VITE_MODE=development uni -p mp-weixin", "build:mp-weixin": "cross-env VITE_MODE=production uni build -p mp-weixin", "build:mp-weixin:chat": "cross-env VITE_BUILD_MODE=chat VITE_MODE=production uni build -p mp-weixin", "check:types": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false", "check:deps": "taze -f", "postinstall": "npx simple-git-hooks", "lint": "eslint .", "lint:fix": "eslint . --fix", "lint:lint-staged": "lint-staged", "lint:style": "stylelint --cache \"**/*.{vue,scss,css}\" --fix", "commit": "git pull && git add -A && git-cz && git push"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-alpha-4050220250208001", "@dcloudio/uni-app-harmony": "3.0.0-alpha-4050220250208001", "@dcloudio/uni-app-plus": "3.0.0-alpha-4050220250208001", "@dcloudio/uni-components": "3.0.0-alpha-4050220250208001", "@dcloudio/uni-h5": "3.0.0-alpha-4050220250208001", "@dcloudio/uni-mp-alipay": "3.0.0-alpha-4050220250208001", "@dcloudio/uni-mp-baidu": "3.0.0-alpha-4050220250208001", "@dcloudio/uni-mp-jd": "3.0.0-alpha-4050220250208001", "@dcloudio/uni-mp-kuaishou": "3.0.0-alpha-4050220250208001", "@dcloudio/uni-mp-lark": "3.0.0-alpha-4050220250208001", "@dcloudio/uni-mp-qq": "3.0.0-alpha-4050220250208001", "@dcloudio/uni-mp-toutiao": "3.0.0-alpha-4050220250208001", "@dcloudio/uni-mp-weixin": "3.0.0-alpha-4050220250208001", "@dcloudio/uni-mp-xhs": "3.0.0-alpha-4050220250208001", "@dcloudio/uni-quickapp-webview": "3.0.0-alpha-4050220250208001", "@dcloudio/uni-ui": "^1.5.7", "@tanstack/vue-query": "4.37.1", "@uni-helper/uni-network": "^0.20.0", "@uni-helper/uni-promises": "^0.2.1", "@uni-helper/uni-use": "^0.19.14", "@vueuse/core": "12.7.0", "change-case": "4.1.2", "core-js": "^3.40.0", "dayjs": "^1.11.13", "lodash": "^4.17.21", "matchmaking-api": "git+http://***************/FRONT/matchmaking-api-render.git", "news-api": "git+http://***************/FRONT/new-center-api-render", "nutui-uniapp": "^1.8.2", "oig-finclip-jssdk": "^1.1.7", "oig-utils": "^0.4.9", "pinia": "2.0.36", "pinia-plugin-persistedstate": "^4.2.0", "qs": "6.5.3", "vue": "3.5.13", "worldrou-api": "git+http://***************/FRONT/worldrou-api-render.git", "youxian-api": "git+http://***************/FRONT/youxian-api-render.git"}, "devDependencies": {"@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@dcloudio/types": "^3.4.14", "@dcloudio/uni-automator": "3.0.0-alpha-4050220250208001", "@dcloudio/uni-cli-shared": "3.0.0-alpha-4050220250208001", "@dcloudio/uni-stacktracey": "3.0.0-alpha-4050220250208001", "@dcloudio/vite-plugin-uni": "3.0.0-alpha-4050220250208001", "@iconify/json": "^2.2.309", "@mini-types/alipay": "^3.0.14", "@types/node": "^20.17.19", "@types/qs": "^6.9.18", "@uni-helper/eslint-config": "^0.4.0", "@uni-helper/uni-app-types": "1.0.0-alpha.6", "@uni-helper/uni-env": "^0.1.7", "@uni-helper/uni-types": "1.0.0-alpha.4", "@uni-helper/unocss-preset-uni": "^0.2.11", "@uni-helper/vite-plugin-uni-components": "^0.2.0", "@uni-helper/vite-plugin-uni-layouts": "^0.1.10", "@uni-helper/vite-plugin-uni-manifest": "^0.2.7", "@uni-helper/vite-plugin-uni-pages": "^0.2.28", "@uni-helper/volar-service-uni-pages": "^0.2.28", "@unocss/core": "66.1.0-beta.7", "@unocss/eslint-config": "^66.0.0", "@vitejs/plugin-vue": "^5.0.4", "@vue/runtime-core": "3.5.13", "@vue/tsconfig": "^0.7.0", "cross-env": "^7.0.3", "cz-git": "^1.11.0", "eslint": "9.20.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-validate-filename": "^1.0.0", "lint-staged": "^15.4.3", "miniprogram-api-typings": "^4.0.5", "sass": "~1.78.0", "simple-git-hooks": "^2.11.1", "stylelint": "^16.9.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^5.1.1", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.1", "stylelint-config-standard-scss": "^13.1.0", "stylelint-order": "^6.0.4", "taze": "^18.6.0", "type-fest": "^4.35.0", "typescript": "5.7.3", "unocss": "^66.0.0", "unplugin-auto-import": "^0.19.0", "unplugin-icons": "^0.22.0", "unplugin-vue-macros": "^2.14.2", "vite": "^6.2.3", "vite-plugin-uni-polyfill": "^0.1.0", "vue-i18n": "^9.14.2", "vue-tsc": "2.2.2"}, "simple-git-hooks": {"pre-commit": "pnpm lint:lint-staged", "commit-msg": "npx commitlint --edit ${1}"}, "config": {"commitizen": {"path": "node_modules/cz-git", "useEmoji": true}}, "finclip": {"test": {"appid": "fc2190743335153605"}, "pre": {"appid": "fc2613711996027525"}, "prod": {"appid": "fc2288147848481477"}}}