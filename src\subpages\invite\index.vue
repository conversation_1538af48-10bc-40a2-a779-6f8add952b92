<template>
  <div class="relative min-h-100vh overflow-hidden bg-#dd322e px-24rpx">
    <image
      class="absolute bottom-0 left-0 right-0 top-0 h-680rpx w-100%"
      :src="coverImage"
    />
    <div class="relative z-1 mt-600rpx flex flex-col items-center">
      <image class="absolute h-926rpx w-700rpx -z-1" :src="inviteCardImage" />
      <div class="mt-340rpx w-full text-center text-26rpx c-#b09457 lh-40rpx">
        当前商户: {{ merchantName }}
      </div>

      <button
        class="relative mt-276rpx h-160rpx w-568rpx border-none bg-transparent text-center text-32rpx c-white fw-bold lh-106rpx after:hidden"
        open-type="share"
      >
        <image class="absolute inset-0 h-160rpx w-full -z-1" :src="inviteBtn" />
        邀请同事加入企业
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { decodeURIObject } from "@/utils/url";

useTitle("邀请成员");
const coverImage = "bg-invite.png".toQiniuUrl();
const inviteCardImage = "bg-invite-card.png".toQiniuUrl();
const inviteBtn = "invite-btn.png".toQiniuUrl();

const userStore = useUserStore();

const { merchantList, activeMerchant } = storeToRefs(userStore);

const merchantCode = ref();

const curMerchant = computed(() => {
  return merchantList.value?.find(
    item => item.partnerCode === merchantCode.value,
  );
});

const merchantName = computed(() => curMerchant.value?.realName);

onLoad(option => {
  const { merchantCode: _merchantCode } = decodeURIObject(option);
  merchantCode.value = _merchantCode || activeMerchant.value?.partnerCode;
});

onShareAppMessage(async () => {
  const rsp = await userApi.ApiUserInviteSharejoinmerchantticketPost({
    merchantCode: merchantCode.value,
  });

  return {
    title: `加入${merchantName.value}，构建全球食品保障体系`,
    path: `/subpages/share/index?ticket=${rsp?.data?.ticket ?? ""}`,
    imageUrl: "share-banner.png".toQiniuUrl(),
  };
});
</script>
