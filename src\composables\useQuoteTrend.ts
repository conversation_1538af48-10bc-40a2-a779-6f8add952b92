import StaticLargeDown from "@/static/large_down.png";
import StaticLargeFlat from "@/static/large_flat.png";
import StaticLargeUp from "@/static/large_up.png";
import QuoteDown from "@/static/quote_down.png";
import QuoteFlat from "@/static/quote_flat.png";
import QuoteUp from "@/static/quote_up.png";
import { isNil } from "lodash";

export enum Trend {
  flat, // 平
  rise, // 涨
  fall, // 跌
}
const LargeDown = "large_down.gif".toQiniuUrl();
const LargeFlat = "large_flat.gif".toQiniuUrl();
const LargeUp = "large_up.gif".toQiniuUrl();
export function useQuoteTrend(rate?: number, value?: number) {
  const validRate = !isNil(rate);
  const validValue = !isNil(value);

  const trend =
    !validRate || rate === 0 ? Trend.flat : rate! > 0 ? Trend.rise : Trend.fall;

  const { red, green, black } = unotheme.colors;
  const color =
    trend === Trend.flat
      ? black.trade
      : trend === Trend.rise
      ? red.quote
      : green.quote;
  const image =
    trend === Trend.flat
      ? QuoteFlat
      : trend === Trend.rise
      ? QuoteUp
      : QuoteDown;
  const largeImage =
    trend === Trend.flat
      ? LargeFlat
      : trend === Trend.rise
      ? LargeUp
      : LargeDown;
  const staticLargeImage =
    trend === Trend.flat
      ? StaticLargeFlat
      : trend === Trend.rise
      ? StaticLargeUp
      : StaticLargeDown;
  const background =
    trend === Trend.flat
      ? "#F4F5F7"
      : trend === Trend.rise
      ? "#FEF4F4"
      : "#E9F6F1";

  const toRatePercent = () => (validRate ? `${rate}%` : "--");
  const toValueString = () => {
    if (value === 0) return "--";
    const sign = !validValue ? "" : value > 0 ? "+" : "";
    const riseFallValue = validValue ? value.toString() : "--";

    return `${sign}${riseFallValue}`;
  };

  return {
    trend,
    background,
    color,
    image,
    toRatePercent,
    toValueString,
    largeImage,
    staticLargeImage,
  };
}
