<template>
  <div class="h-100vh">
    <SearchBar v-model="searchValue" @search="search" />
    <NoData v-if="empty" :loading="loading" />

    <scroll-view v-else scroll-y style="height: calc(100vh - 50px)">
      <div class="h-40rpx w-full" />
      <SearchMatchGroup v-if="matchList.length > 0" :products="matchList" />
      <SearchNewGroup v-if="newsList.length > 0" :news="newsList" />
      <SearchQuoteGroup v-if="quoteList.length > 0" :quotes="quoteList" />
      <nut-safe-area position="bottom" />
    </scroll-view>
  </div>
</template>

<script setup lang="ts">
import type { MatchTypes, PlatformTypes, QueryBackendTypes } from "@/api";
import { useHistory } from "@/composables/useSearchHistory";
import SearchMatchGroup from "./components/Group/SearchMatchGroup.vue";
import SearchNewGroup from "./components/Group/SearchNewGroup.vue";
import SearchQuoteGroup from "./components/Group/SearchQuoteGroup.vue";
import SearchBar from "./components/SearchBar.vue";

useTitle(t("sou-suo-jie-guo"));
const history = useHistory("home");
const searchValue = ref("");
const pagination = new Pagination({ pageSize: 3 });

const matchList = ref<MatchTypes.ProductAppRsp[]>([]);
const newsList = ref<QueryBackendTypes.IArticleVo[]>([]);
const quoteList = ref<PlatformTypes.IPriceSituationAvgH5Vo[]>([]);

const loading = ref(false);

const empty = computed(
  () =>
    matchList.value.length === 0 &&
    newsList.value.length === 0 &&
    quoteList.value.length === 0,
);

function search(text: string) {
  history.add(text);
  searchList();
}

onLoad(options => {
  searchValue.value = decodeURIComponent(options?.value ?? "");
  if (searchValue.value) {
    searchValue.value = searchValue.value.trim();
    searchList();
  }
});

function searchList() {
  loading.value = true;
  Promise.all([searchMatchList(), searchNewsList(), searchQuoteList()]).finally(
    () => {
      loading.value = false;
    },
  );
}

async function searchQuoteList() {
  try {
    const rsp = await platformApi.ApiPriceSituationPageAvgPageLimitPost(
      {
        globalSearch: searchValue.value,
      },
      {
        page: pagination.currentPage,
        limit: pagination.pageSize,
      },
    );
    if (rsp.data?.list?.length) {
      quoteList.value = rsp.data?.list ?? [];
    }
  } catch {
    matchList.value = [];
  }
}

async function searchMatchList() {
  try {
    const rsp = await matchApi.apiAppSearchSearchPurchasePost({
      pageNo: pagination.currentPage,
      pageSize: pagination.pageSize,
      productName: searchValue.value,
    });

    if (rsp.data?.list?.length) {
      matchList.value = rsp.data?.list ?? [];
    }
  } catch {
    matchList.value = [];
  }
}

async function searchNewsList() {
  try {
    const rsp =
      await queryBackendApi.ApiQuerytoolNewsqueryPagenewsbytypePageLimitPost(
        {
          articleTitle: searchValue.value,
          contentTypeList: "YOUXIANGO",
        },
        {
          page: pagination.currentPage,
          limit: pagination.pageSize,
        },
      );
    if (rsp.data?.list?.length) {
      newsList.value = rsp.data?.list ?? [];
    }
  } catch {}
}
</script>
