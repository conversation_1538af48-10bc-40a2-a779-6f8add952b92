type Channel = "home";

export function useHistory(channel: Channel, limit = 10) {
  const list = ref<string[]>([]);
  const STORAGE_KEY = `search_history_${channel}`;
  const storage = uni.getStorageSync(STORAGE_KEY) ?? "";

  if (storage) {
    try {
      list.value = JSON.parse(storage) ?? [];
    } catch {}
  }

  function add(text: string) {
    text = text.trim();
    if (!text) return;
    const _history = list.value;
    if (_history.includes(text)) {
      _history.splice(_history.indexOf(text), 1);
      _history.unshift(text);
    } else {
      _history.unshift(text);
      if (_history.length > limit) {
        _history.splice(limit);
      }
    }

    uni.setStorageSync(STORAGE_KEY, JSON.stringify(_history));
  }

  function clear() {
    list.value = [];
    uni.removeStorage({
      key: STORAGE_KEY,
    });
  }

  return {
    list,
    add,
    clear,
  };
}
