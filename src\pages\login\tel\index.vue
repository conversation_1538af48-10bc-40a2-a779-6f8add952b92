<template>
  <div class="h-full bg-white px-30rpx">
    <nut-tabs v-model="tab" align="left" :title-gutter="5">
      <nut-tab-pane
        v-for="item in tabs"
        :key="item.value"
        :pane-key="item.value"
        :title="item.title"
      />
    </nut-tabs>
    <nut-form ref="ruleForm" :rules="rules" :model-value="formData">
      <FormItem v-model="formData.phone" :item="formConfig.phone" />
      <FormItem v-if="Number(tab) === LoginWay.sms" :item="formConfig.code">
        <nut-row type="flex" align="center" gutter="10">
          <nut-col :span="14">
            <nut-input
              v-model="formData.code"
              :placeholder="formConfig.code.placeholder"
              max-length="6"
              type="number"
              clearable
            />
          </nut-col>
          <nut-col :span="10">
            <SmsBtn
              :payload="{ phoneNumber: formData.phone, codeUseType: 'LOGIN' }"
            />
          </nut-col>
        </nut-row>
      </FormItem>
      <FormItem
        v-else
        v-model="formData.password"
        :item="formConfig.password"
      />
    </nut-form>

    <FormSubmitBtn :gutter-x="false" :loading="loading" @click="handleLogin">
      {{ $t("deng-lu") }}
    </FormSubmitBtn>
    <div
      v-if="Number(tab) === LoginWay.password"
      class="mt-20rpx f-c justify-end"
    >
      <div class="text-30rpx color-grey" @click="toForgetPage">
        {{ $t("wang-ji-mi-ma") }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { FormInst } from "nutui-uniapp";
import SmsBtn from "@/components/SmsBtn.vue";
import { LoginWay } from "@/enums";

useTitle(t("deng-lu-you-ding-te"));

let redirect = "";

onLoad(options => {
  if (typeof options?.redirect === "string") {
    redirect = decodeURIComponent(options?.redirect);
  }
});

const { loginSuccess } = useEntry();
const ruleForm = ref<FormInst | null>(null);
const tab = ref(LoginWay.sms);
const loading = ref(false);
const tabs = ref([
  {
    title: t("yan-zheng-ma-deng-lu"),
    value: LoginWay.sms,
  },
  {
    title: t("mi-ma-deng-lu"),
    value: LoginWay.password,
  },
]);

const { formData, formConfig, rules } = useForm({
  phone: {
    label: t("shou-ji-hao"),
    type: "number",
    rules: [FormRules.required(), FormRules.isPhone()],
  },
  code: {
    label: t("duan-xin-yan-zheng-ma"),
    rules: [FormRules.required()],
  },
  password: {
    label: t("mi-ma"),
    type: "password",
    rules: [FormRules.required(), FormRules.passwordLength()],
  },
});

function toForgetPage() {
  uni.navigateTo({
    url: "/subpages/password/forget",
  });
}

async function handleLogin() {
  ruleForm.value?.validate().then(async ({ valid }) => {
    if (valid) {
      loading.value = true;
      try {
        if (Number(tab.value) === LoginWay.sms) {
          await smsLogin();
        } else if (Number(tab.value) === LoginWay.password) {
          await pwdLogin();
        }

        loginSuccess({ redirect, withToken: true });
      } finally {
        loading.value = false;
      }
    }
  });
}

async function pwdLogin() {
  const rsp = await userApi.ApiUserLoginPost(
    {
      mobileNum: formData.phone,
      password: formData.password,
    },
    {
      showError: true,
    },
  );

  return rsp.data;
}

async function smsLogin() {
  const rsp = await userApi.ApiUserSmsloginPost(
    {
      mobileNum: formData.phone,
      smsCode: formData.code,
    },
    {
      showError: true,
    },
  );
  return rsp.data;
}
</script>

<style lang="scss" scoped>
view {
  --nut-tabs-horizontal-titles-item-min-width: 200rpx;
  --nut-tabs-horizontal-titles-item-active-line-width: 70%;
  --nut-tabs-titles-item-font-size: 36rpx;
  --nut-tabs-horizontal-titles-height: 100rpx;
  --nut-tab-pane-padding: 0px;
}

::v-deep .nut-cell {
  min-height: 110rpx;
}
</style>
