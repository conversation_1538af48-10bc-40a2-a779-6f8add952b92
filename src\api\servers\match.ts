import { Api as Match<PERSON><PERSON> } from "matchmaking-api/src/base-server";
import { errorAdapter } from "../interceptors/error";
import { requestInterceptor } from "../interceptors/request";
import { responseAdapter } from "../interceptors/response";

const match = new MatchApi({
  baseURL: import.meta.env.VITE_MATCH_GATWAY,
});

// @ts-ignore
match.instance.interceptors.request.use(config => requestInterceptor(config));
// @ts-ignore
match.instance.interceptors.response.use(responseAdapter, errorAdapter);
export const matchApi = match.api;
