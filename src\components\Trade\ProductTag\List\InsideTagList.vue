<template>
  <div class="flex flex-wrap gap-12rpx">
    <template v-if="clickable">
      <ClickableTag
        v-for="tag in tags.insideTagTextList"
        :key="tag.text"
        :item="tag"
        @trigger="showIntroductionDialog = true"
      />
    </template>

    <template v-else>
      <FeatureTag v-for="tag in tags.insideTagTextList" :key="tag.text">
        {{ tag.text }}
      </FeatureTag>
    </template>

    <SmartTag v-if="tags.showStrictTag" />

    <nut-dialog
      v-model:visible="showIntroductionDialog"
      :title="$t('biao-qian-shuo-ming')"
      :ok-text="$t('wo-zhi-dao-le')"
      no-cancel-btn
    >
      <TagIntroduction />
    </nut-dialog>
  </div>
</template>

<script setup lang="ts">
import ClickableTag from "@/components/Trade/ProductTag/ClickableTag.vue";
import FeatureTag from "@/components/Trade/ProductTag/FeatureTag.vue";
import TagIntroduction from "../../TagIntroduction.vue";
import SmartTag from "../SmartTag.vue";

defineProps<{
  tags: ReturnType<typeof useTradeProduct>["tags"];
  clickable?: boolean;
}>();

const showIntroductionDialog = ref(false);
</script>
