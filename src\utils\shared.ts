// 防止快速点击
let lastClickTime = 0;

export function isFastClick(num = 1000) {
  const time = new Date().getTime();
  if (time - lastClickTime > num) return false;

  lastClickTime = time;
  return true;
}

// 手机号码脱敏
export function maskPhoneNumber(
  phoneNumber: string,
  numToHide: number = 4,
): string {
  if (numToHide < 0 || numToHide > phoneNumber.length - 3) {
    return phoneNumber;
  }

  const visiblePart = phoneNumber.substring(0, 2);
  const hiddenPart = "*".repeat(numToHide);
  const remainingPart = phoneNumber.substring(2 + numToHide);

  return visiblePart + hiddenPart + remainingPart;
}

export function uniqueId() {
  return Math.random().toString(36).substring(2, 15);
}

export interface UniversalScheme {
  content: "https" | "weapp";
  path: string;
  appId?: string;
}

export function parseUniversalScheme(url: string): UniversalScheme {
  // 处理 https:// 协议
  if (url.startsWith("https://")) {
    return {
      content: "https",
      path: url, // 返回完整 URL 作为路径
    };
  }

  // 处理 weapp:// 协议
  if (url.startsWith("weapp://")) {
    const content = url.slice(8);
    const firstSlashIndex = content.indexOf("/");

    // 处理无路径情况 (如: weapp://appid)
    if (firstSlashIndex === -1) {
      return {
        content: "weapp",
        appId: content,
        path: "/",
      };
    }

    // 提取 appId 和 path
    const appId = content.substring(0, firstSlashIndex);
    let path = content.substring(firstSlashIndex);

    // 规范化路径 (确保以斜杠开头)
    if (!path.startsWith("/")) path = `/${path}`;

    return {
      content: "weapp",
      appId,
      path: path.split("?")[0], // 移除查询参数
    };
  }

  // 处理未知协议
  throw new Error(`Unsupported URL schema: ${url}`);
}

/**
 * 按指定字段分组并展平为一维数组，每个分组前插入组名项
 * @param data 输入的数据数组
 * @param key 分组依据的字段名
 * @returns 一维数组，包含组名项（带 isGroup: true）和数据项，按分组字段值排序
 */
type DataItem = Record<string, any>;
type GroupedItem<T extends DataItem> = {
  isGroup?: boolean;
} & {
  [key in keyof T]: T[key];
};
export function groupByKey<T extends DataItem>(
  data: T[],
  key: keyof T,
): GroupedItem<T>[] {
  // 使用 Map 进行分组
  const groupMap = new Map<any, T[]>();

  // 遍历数据，按指定字段分组
  data.forEach(item => {
    const keyValue = item[key];
    if (!groupMap.has(keyValue)) {
      groupMap.set(keyValue, []);
    }
    groupMap.get(keyValue)!.push(item);
  });

  // 将 Map 转换为一维数组，插入组名项
  const result = Array.from(groupMap.entries())
    // 展平并插入组名项
    .flatMap(([keyValue, items]) => [
      { [key]: keyValue, isGroup: true } as GroupedItem<T>, // 插入组名项
      ...items,
    ]);

  return result;
}
