import type { ToastInst, ToastOptions } from "nutui-uniapp";

let rootNutuiToast: ToastInst;

export function setRootNutuiToast(toast: ToastInst) {
  rootNutuiToast = toast;
}

export default function showMsg(
  content: string,
  options: Partial<
    Omit<ToastOptions, "icon"> & {
      useDialog?: boolean;
      useToast?: boolean;
      icon: UniNamespace.ShowToastOptions["icon"];
    }
  > = {},
) {
  const duration = 2000;
  if (options.useDialog) {
    uni.showModal({
      title: t("ti-shi"),
      content,
      showCancel: false,
    });
    return;
  }
  // 根据字符串选择不同的toast方式
  if (content.length <= 10 || options.useToast) {
    uni.showToast({
      title: content,
      duration,
      icon: options.icon || "none",
    });
  } else {
    rootNutuiToast?.text(content, {
      duration,
      ...options,
    });
  }
}
