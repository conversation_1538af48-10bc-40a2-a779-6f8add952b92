<template>
  <view
    class="box-border h-100vh overflow-hidden bg-contain bg-no-repeat"
    :style="{
      backgroundImage,
      paddingTop: `${menuButtonBounding?.top}px`,
    }"
  >
    <slot />
  </view>
</template>

<script setup lang="ts">
const props = defineProps<{ background: keyof typeof IMAGES | string }>();
const { menuButtonBounding } = storeToRefs(useAppStore());

const IMAGES: Record<string, string> = {
  HOME: "home_bgd.png".toQiniuUrl(),
  PROXY: "proxy_bgd.png".toQiniuUrl(),
  MINE: "mine_bgd.png".toQiniuUrl(),
  VIP: "vip_bgd.png".toQiniuUrl(),
};

const backgroundImage = computed(() => {
  return props.background
    ? `url(${IMAGES[props.background] ?? props.background})`
    : "none";
});

onLoad(() => {
  // 预加载图片
  wx.preloadAssets({
    data: [IMAGES.PROXY, IMAGES.MINE].map(src => {
      return {
        type: "image",
        src,
      };
    }),
  });
});
</script>
