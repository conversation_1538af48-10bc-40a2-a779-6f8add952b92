<template>
  <div v-if="product && rspData" class="p-10rpx">
    <TradeBaseInfo :rsp-data="rspData" :product="product" />
    <TitleCard :title="$t('shang-pin-xin-xi')">
      <div class="px-20rpx pb-24rpx">
        <InsideTagList :tags="product.tags" clickable />
        <TradeProductAttrs :data="rspData" />
      </div>
    </TitleCard>
    <TradeRemark v-if="rspData.remark" :remark="rspData.remark" />
    <TradeBottom @call-phone="callPhone" />
  </div>
</template>

<script setup lang="ts">
import type { MatchTypes } from "@/api";
import InsideTagList from "@/components/Trade/ProductTag/List/InsideTagList.vue";
import TradeBaseInfo from "./components/TradeBaseInfo.vue";
import TradeBottom from "./components/TradeBottom.vue";
import TradeProductAttrs from "./components/TradeProductAttrs.vue";
import TradeRemark from "./components/TradeRemark.vue";

useTitle(t("qiu-gou-xiang-qing"));
const rspData = ref<MatchTypes.PurchaseProductAppRsp>();
const product = computed(() =>
  rspData.value ? usePurchaseProduct(rspData.value) : null,
);

async function queryDetail(id: string) {
  if (id) {
    const rsp = await matchApi.apiAppPurchaseProductDetailPost(
      {
        id: Number(id),
      },
      {
        showLoading: true,
      },
    );
    rspData.value = rsp.data;
  }
}

async function callPhone() {
  const rsp = await matchApi.apiAppPurchaseProductGetContactsPhonePost(
    {
      id: rspData.value?.id,
    },
    { showError: true, showLoading: true },
  );
  if (rsp.data?.contactsPhone) {
    uni.makePhoneCall({
      phoneNumber: rsp.data?.contactsPhone,
    });
  }
}

onLoad(options => {
  queryDetail(options?.id);
});

onShareAppMessage(options =>
  useShareAppMessage(options, { useCurrentPath: true }),
);
</script>
