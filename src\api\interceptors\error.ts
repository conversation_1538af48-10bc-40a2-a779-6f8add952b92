import { ResponseCode } from "@/api/constant";

export function errorAdapter(err: any) {
  const LOGOUT_STATUS = [ResponseCode.unauthorized];
  const WHITE_URL_LIST = ["/api/user/logout"]; // 不处理logout的接口

  const { config, response } = err;
  const { data, status } = response ?? "";
  const { url } = config;

  if (config.showLoading) {
    uni.hideLoading();
  }

  if (
    LOGOUT_STATUS.includes(status) &&
    WHITE_URL_LIST.every(item => !url.endsWith(item))
  ) {
    useUserStore().logout();
    useNavigate().redirectToIndex();
    return Promise.reject(err);
  } else if (config?.showError) {
    showMsg(data?.msg || "unknown error", {
      duration: 3000,
    });
  }

  return Promise.reject(err);
}
