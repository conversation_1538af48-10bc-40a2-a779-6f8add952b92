import type { UserTypes } from "@/api";
import type { ButtonOnGetphonenumberEvent } from "@uni-helper/uni-app-types";
import type { AxiosError } from "axios";
import { delay } from "oig-utils";

export function useWxLogin() {
  const { loginSuccess } = useEntry();
  let userProfile: UniApp.GetUserProfileRes | null = null;
  let loginResult: UniApp.LoginRes | null = null;
  let wxUserInfo: UserTypes.IDecodeWeChatUserInfoVo | null = null;

  onLoad(wxLogin); // 解决pad block corrupted报错

  // 获取用户基本信息
  function getUserProfile() {
    return new Promise<UniApp.GetUserProfileRes>((resolve, reject) => {
      const rejectHandler = () => reject(new Error("getUserProfile fail"));
      uni.getUserProfile({
        lang: "zh_CN",
        desc: "用于授权登录小程序",
        success(result) {
          if (result.errMsg === "getUserProfile:ok") {
            userProfile = result;
            resolve(result);
          } else {
            rejectHandler();
          }
        },
        fail() {
          rejectHandler();
        },
      });
    });
  }

  // 执行微信登录，获取code
  function wxLogin() {
    return new Promise<UniApp.LoginRes>((resolve, reject) => {
      const rejectHandler = () => reject(new Error("wxLogin fail"));

      uni.login({
        provider: "weixin",
        success(result) {
          if (result.errMsg === "login:ok") {
            loginResult = result;
            resolve(result);
          } else {
            rejectHandler();
          }
        },
        fail() {
          rejectHandler();
        },
      });
    });
  }

  // 访问接口，获取用户信息
  async function fetchWxUserInfo(
    options: {
      retry: boolean;
    } = { retry: false },
  ): Promise<UserTypes.IDecodeWeChatUserInfoVo | null> {
    try {
      const rsp = await userApi.ApiWechatWeappDecodeuserinfoPost({
        iv: userProfile?.iv,
        encryptedData: userProfile?.encryptedData,
        code: loginResult?.code,
        userProfileInfoDto: userProfile?.userInfo,
      });
      return rsp.data;
    } catch (err) {
      // 接口返回错误，主动重试一次
      const errorMsg = (err as AxiosError).response?.data?.msg;
      const isFirstRequest = !options.retry;
      if (isFirstRequest) {
        await delay(1500);
        return await retryWxLoginTask();
      } else {
        showMsg(errorMsg);
      }
    }

    return null;
  }

  // 访问接口，绑定手机号码
  async function bindPhoneByApi() {
    if (!wxUserInfo?.unionId) {
      return;
    }

    return userApi.ApiWechatWeappDecodephonePost(
      {
        iv: userProfile?.iv,
        encryptedData: userProfile?.encryptedData,
        code: loginResult?.code,
        unionId: wxUserInfo.unionId,
      },
      { showError: true },
    );
  }

  // 重试微信登录任务
  async function retryWxLoginTask() {
    await wxLogin();
    return await fetchWxUserInfo({ retry: true });
  }

  // 执行微信登录任务
  async function runWxLoginTask() {
    await getUserProfile();
    await wxLogin();
    wxUserInfo = await fetchWxUserInfo();
    return wxUserInfo;
  }

  // 微信「获取手机号码」事件回调，调取接口绑定手机号码
  async function runBindPhoneTask(event: ButtonOnGetphonenumberEvent) {
    const { iv, encryptedData, errMsg } = event?.detail;
    if (userProfile) {
      userProfile.iv = iv ?? userProfile.iv;
      userProfile.encryptedData = encryptedData ?? userProfile.encryptedData;
    }

    if (errMsg === "getPhoneNumber:ok") {
      await wxLogin();
      await bindPhoneByApi();
    }
  }

  return {
    runWxLoginTask,
    runBindPhoneTask,
  };
}
