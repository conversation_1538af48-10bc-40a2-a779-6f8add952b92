<template>
  <div class="fixed inset-0 flex justify-center bg-#33373f">
    <div
      class="mt-40rpx w-700rpx flex flex-col items-center bg-[length:100%_100%] bg-no-repeat -translate-x-50%"
      :style="{ backgroundImage: `url(${coverImage})` }"
    >
      <div
        class="mx-auto mb-22rpx mt-330rpx h-180rpx w-180rpx bg-[length:100%_100%]"
        :style="{ backgroundImage: `url(${approvedImage})` }"
      />
      <div :class="textClass">您已经是该商户员工</div>
      <div :class="textClass">无需再次审核</div>
      <nut-button
        custom-class="mt-30rpx! mx-auto! p-[18rpx_38rpx_14rpx]! lh-40rpx! text-28rpx! c-#ff7451! b-#ff7451! rounded-36rpx! bg-white!"
        @click="handleBack"
      >
        返回首页
      </nut-button>
    </div>
  </div>
</template>

<script setup lang="ts">
const coverImage = "/bg-success.png".toQiniuUrl({ suffixPath: "/assets" });
const approvedImage = "/approved.png".toQiniuUrl({ suffixPath: "/assets" });

const textClass = "text-26rpx c-grey lh-46rpx text-center";

const { redirectToIndex } = useNavigate();
function handleBack() {
  redirectToIndex();
}
</script>
