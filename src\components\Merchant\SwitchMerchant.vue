<template>
  <merchant-popup
    :show="show"
    :list="merchantList"
    @switch="handleSwitchMerchant"
    @close="show = false"
    @add="handleAddMerchant"
  />
</template>

<script setup lang="ts">
import { Config } from "@/config";

const emit = defineEmits<{ switchSuccess: [value: string] }>();
const userStore = useUserStore();
const { merchantList } = storeToRefs(userStore);
const { openWebview } = useNavigate({ webview: { withToken: true } });

const show = defineModel<boolean>({ required: true });

async function handleSwitchMerchant(e: any) {
  const partnerCode = e.detail?.data?.partnerCode ?? "";
  await userApi.ApiUserSwitchpartnerPost(
    { partnerCode },
    { showLoading: true, showError: true },
  );
  show.value = false;
  userStore.queryMerchantList();
  emit("switchSuccess", partnerCode);
}

function handleAddMerchant() {
  openWebview({
    url: `${Config.merchantUrl}/registration`,
  });
}
</script>
