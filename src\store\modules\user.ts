import type { MerchantTypes, UserTypes } from "@/api";
import { MerchantEntityTypeEnum, RoleEnum, ServiceTypeEnum } from "@/enums";

const STORAGE_TOKEN = "token";

export const useUserStore = defineStore("user", () => {
  const token = ref<string>(uni.getStorageSync(STORAGE_TOKEN) || "");
  const userInfo = ref<UserTypes.IUserInfoVo | null>(null);
  const unReadCount = ref<number>(0);

  const setToken = (_: string) => {
    uni.setStorageSync(STORAGE_TOKEN, _);
    token.value = _;
  };

  const setUserInfo = (_: UserTypes.IUserInfoVo | null) => {
    userInfo.value = _;
  };

  const setUnReadCount = (_: number) => {
    unReadCount.value = _;
  };

  const queryUserInfo = async () => {
    const rsp = await userApi.ApiUserInfoGet();
    setUserInfo(rsp.data);
  };

  const logout = async ({ manually }: { manually?: boolean } = {}) => {
    if (manually && token.value) {
      try {
        await userApi.ApiUserLogoutPost();
      } catch {}
    }

    setToken("");
    setUserInfo(null);
    setUnReadCount(0);
  };

  const relogin = (token: string) => {
    logout();
    nextTick(() => {
      setToken(token);
      useEntry().runQueryTask();
    });
  };

  const isLogined = computed(() => token.value?.length > 0);

  const merchantList: Ref<MerchantTypes.IUserJoinMerchantInfoRsp[]> = ref([]);

  const activeMerchant = computed(() =>
    merchantList.value.find(item => item.currentActive),
  );

  const isPersonalEntity = computed(
    () =>
      activeMerchant.value?.realnameAuthEntityType ===
      MerchantEntityTypeEnum.PERSONAL,
  );

  const hasRolePermission = (role: RoleEnum) => {
    if (!activeMerchant.value) return false;
    const { hasAdminRole, hasSuperAdminRole, roles } = activeMerchant.value;
    return (
      hasAdminRole ||
      hasSuperAdminRole ||
      (roles?.some(item => item.roleCode === role) ?? false)
    );
  };

  // 资金账户权限
  const hasPayPermission = computed(() => {
    return hasRolePermission(RoleEnum.PAY);
  });

  // 代理权限 (商户开通代理服务, 且用户有代理角色)
  const hasUchainPermission = computed(() => {
    return (
      activeMerchant.value?.openService?.some(
        item => item.serviceCode === ServiceTypeEnum.AGENT,
      ) && hasRolePermission(RoleEnum.UCHAIN)
    );
  });

  const queryMerchantList = async () => {
    // 需要请求商户中心和用户中心的商户列表接口, 触发后端逻辑
    const [rsp] = await Promise.all([
      merchantApi.ApiAppmerchantGetjoinedmerchantlistPost(),
      userApi.ApiUserJoinedpartnerlistGet(),
    ]);
    merchantList.value = rsp?.data ?? [];
  };

  const fundAccountUrl = ref<string | undefined>();

  // 查询资金账户地址
  const queryFundAccountUrl = async () => {
    const { partnerCode: merchantCode } = activeMerchant.value ?? {};

    if (isPersonalEntity.value || !hasPayPermission.value || !merchantCode) {
      fundAccountUrl.value = undefined;
      return;
    }

    const rsp = await merchantApi.ApiApppayGetaccounturlGet({ merchantCode });
    fundAccountUrl.value = rsp?.data;
  };

  // 查询消息未读数量
  const queryUnreadCount = async () => {
    const rsp = await platformApi.ApiMessageUnreadappmessagecountbyapptypePost({
      userId: userInfo.value?.userId,
    });
    setUnReadCount(rsp?.data?.count ?? 0);
  };

  return {
    token,
    userInfo,
    relogin,
    logout,
    setToken,
    isLogined,
    setUserInfo,
    queryUserInfo,
    merchantList,
    activeMerchant,
    isPersonalEntity,
    queryMerchantList,
    fundAccountUrl,
    hasPayPermission,
    hasUchainPermission,
    queryFundAccountUrl,
    unReadCount,
    queryUnreadCount,
  };
});
