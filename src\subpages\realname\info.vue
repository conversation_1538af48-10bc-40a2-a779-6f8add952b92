<template>
  <div class="px-12px">
    <nut-cell-group>
      <template v-for="(row, index) in list" :key="index">
        <nut-cell
          :title="row.title"
          center
          title-width="100px"
          desc-text-align="left"
        >
          <template #desc>
            {{ row.value ?? "--" }}
          </template>
        </nut-cell>
      </template>
    </nut-cell-group>

    <template v-if="realnamePending">
      <nut-button
        block
        size="large"
        type="primary"
        shape="square"
        @click="continueVerification"
      >
        {{ $t("ji-xu-ren-zheng") }}
      </nut-button>
      <div class="h-12px" />
      <nut-button
        plain
        block
        size="large"
        type="primary"
        shape="square"
        :loading="cancelLoading"
        @click="cancelAuthentication"
      >
        {{ $t("xiu-gai-ren-zheng-xin-xi") }}
      </nut-button>
    </template>
  </div>
</template>

<script setup lang="ts">
import type { UserTypes } from "@/api";
import { AUTH_CARD_TYPE_MAP, AUTH_STATUS_MAP, RealNameAuth } from "@/enums";
import { useToggle } from "@vueuse/core";

useTitle(t("shi-ming-ren-zheng-xin-xi"));

const { userInfo } = storeToRefs(useUserStore());
const { openWebview } = useNavigate();
const realnameInfo = ref<UserTypes.IPersonalRealnameAuthInfoVo>();
const realnamePending = computed(
  () => unref(realnameInfo)?.realnameAuthStatus === RealNameAuth.PENDING,
);

async function fetchData() {
  try {
    const { data } = await userApi.ApiUserRealnameauthinfoGet(
      {},
      { showLoading: true },
    );
    realnameInfo.value = data ?? {};
  } catch {}
}

const list = computed(() => {
  const { realnameAuthStatus, realName, credentialType, credentialId } =
    realnameInfo?.value ?? {};
  return [
    { title: t("ren-zheng-lei-xing"), value: t("shi-ming-ren-zheng") },
    {
      title: t("ren-zheng-zhuang-tai"),
      value: AUTH_STATUS_MAP[realnameAuthStatus ?? ""] ?? "",
    },
    { title: t("zhen-shi-xing-ming"), value: realName ?? "" },
    {
      title: t("zheng-jian-lei-xing"),
      value: AUTH_CARD_TYPE_MAP[credentialType ?? ""] ?? "",
    },
    { title: t("zheng-jian-hao-ma"), value: credentialId ?? "" },
  ];
});

const [cancelLoading, toggleCancelLoading] = useToggle(false);
function continueVerification() {
  if (cancelLoading.value) return;

  const url = unref(realnameInfo)?.realnameAuthUrl ?? "";
  openWebview({
    url,
  });
}

async function cancelAuthentication() {
  if (cancelLoading.value) return;

  try {
    toggleCancelLoading(true);
    const userId = unref(userInfo)!.userId!;
    await userApi.ApiAuthCancelpersonalauthPost({ userId });
    useUserStore().queryUserInfo();
    uni.redirectTo({
      url: "/subpages/realname/form",
    });
  } catch {}
  toggleCancelLoading(false);
}

onMounted(() => {
  fetchData();
});
</script>
