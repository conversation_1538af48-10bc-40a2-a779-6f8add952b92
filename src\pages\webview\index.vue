<template>
  <web-view v-if="webviewUrl" :src="webviewUrl" @message="onMessage" />
</template>

<script setup lang="ts">
import { XHeader } from "@/api/constant";
import { RealNameTypeEnum } from "@/enums";
import {
  addQueryParam,
  decodeURIObject,
  parseUrl,
  removeQueryParam,
} from "@/utils/url";
import { webviewShareList } from "./share.config";

interface ShareData {
  title: string;
  desc?: string;
  url: string;
  imageUrl?: string;
  extraData?: Record<string, string>;
}

const webviewUrl = ref("");
const urlParams = ref();
const { token, relogin } = useUserStore();
const shareData = ref<ShareData>({
  title: "",
  url: "",
});

onLoad(option => {
  const { title, url, withToken, n, scene } = decodeURIObject(option);
  console.log("option", option);
  if (typeof title === "string") {
    useTitle(title);
  }
  // 小程序码参数
  if (scene) {
    const queryStr = decodeURIComponent(scene);
    const query: { [key: string]: unknown } = {};
    queryStr
      ?.split("&")
      .map(i => i.split("="))
      .forEach(i => (query[i[0]] = i[1]));

    const { n } = query;
    if (n === "t") {
      getAuthUrl(query);
    }
  }
  // 对接管理后台邀请认证
  if (n === "t") {
    getAuthUrl(option);
  }
  if (typeof url === "string") {
    let urlValue = url;
    if (withToken === "true") {
      urlValue = url.toParamsUrl({
        [XHeader.HToken]: token,
      });
    }
    webviewUrl.value = urlValue;
  }
});

async function getAuthUrl(option: any) {
  try {
    const { q, t } = decodeURIObject(option);
    const { data } = await userApi.ApiAuthGetrealnameurlPost(
      {
        queryId: q,
        type: t === "M" ? RealNameTypeEnum.MERCHANT : RealNameTypeEnum.USER,
      },
      { showError: true },
    );
    if (!data?.redirect) return false;
    webviewUrl.value = data.redirect;
  } catch (error) {
    console.error(error);
  }
}

function onMessage(event: any) {
  if (typeof event === "object") {
    const payload = event.detail?.data?.at(-1);
    const data = payload?.data ?? {};
    console.log("payload", JSON.stringify(payload));

    switch (payload.event) {
      case "relogin":
        relogin(payload.data.token ?? "");
        break;
      case "urlParams":
        urlParams.value = payload.data;
        break;
      case "saveImage":
        handleSaveImage(data.base64);
        break;
      case "share":
        handleShareData(data);
        break;
      default:
    }
  }
}

function handleSaveImage(data: string) {
  wx.showLoading({ title: "保存中..." });

  try {
    // 移除 base64 前缀
    const base64 = data.replace(/^data:image\/\w+;base64,/, "");

    // 写入临时文件
    const tempFilePath = `${wx.env.USER_DATA_PATH}/ydt_image_${Date.now()}.png`;

    wx.getFileSystemManager().writeFile({
      filePath: tempFilePath,
      data: base64,
      encoding: "base64",
      success: _ => {
        // 保存到相册
        wx.saveImageToPhotosAlbum({
          filePath: tempFilePath,
          success: () => {
            wx.hideLoading();
            wx.showToast({
              title: t("bao-cun-cheng-gong"),
              icon: "success",
            });
          },
          fail: err => {
            wx.hideLoading();
            console.error("保存失败:", err);
            wx.showToast({
              title: t("bao-cun-shi-bai"),
              icon: "error",
            });
          },
        });
      },
      fail: err => {
        wx.hideLoading();
        console.error("写入文件失败:", err);
      },
    });
  } catch (error) {
    wx.hideLoading();
    console.error("处理图片数据失败:", error);
  }
}

function handleShareData(options: ShareData) {
  shareData.value = { ...options };
}

onShareAppMessage(async options => {
  if (shareData.value?.url !== "") {
    let path = `/pages/webview/index?url=${encodeURIComponent(
      shareData.value.url,
    )}`;
    for (const key in shareData.value.extraData) {
      path = addQueryParam(path, key, shareData.value.extraData[key]);
    }
    return {
      title: shareData.value.title,
      desc: shareData.value.desc,
      path,
      imageUrl: shareData.value.imageUrl,
    };
  }

  let path = defaultShareOptions.path;
  const { childPath } = parseUrl(options.webViewUrl!);
  let url = options.webViewUrl!;
  if (urlParams.value) {
    for (const key in urlParams.value) {
      url = addQueryParam(url, key, urlParams.value[key]);
    }
  }
  if (webviewShareList.some(item => childPath.startsWith(item))) {
    path = `/pages/webview/index?url=${encodeURIComponent(
      removeQueryParam(url, XHeader.HToken),
    )}`;
  }

  return {
    title: defaultShareOptions.title,
    path,
  };
});
</script>
