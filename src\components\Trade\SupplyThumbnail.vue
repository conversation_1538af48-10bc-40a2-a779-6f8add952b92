<template>
  <div class="relative">
    <image
      mode="aspectFill"
      class="size-200rpx border-2rpx border-grey-light rounded-[8rpx]"
      :src="thumbnail.url?.toThumbnailUrl()"
    />
    <div
      v-if="thumbnail.titles.length > 0"
      class="absolute bottom-14rpx left-10rpx w-90% text-22rpx color-white font-300"
    >
      <div v-for="title in thumbnail.titles" :key="title" class="text-ellipsis">
        {{ title }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { MatchTypes } from "@/api";

const { data } = defineProps<{
  data: MatchTypes.ProductAppRsp | MatchTypes.SupplyProductAppRsp;
}>();
const { thumbnail } = useSupplyImage(data);
</script>
