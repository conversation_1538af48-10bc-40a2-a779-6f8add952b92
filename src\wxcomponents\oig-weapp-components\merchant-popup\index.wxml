<van-popup show="{{visible}}" custom-style="{{'z-index: 1000;' + 'height: ' + height + ';'}}" position="bottom" round="{{true}}" close-on-click-overlay="{{true}}" safe-area-inset-bottom="{{false}}" bind:close="handleClose">
  <view class="head-box">
    <view class="title">我的商户</view>
    <view wx:if="{{showAdd}}" class="add-btn" bind:tap="handleAddMerchant">
      <image class="add-btn-icon" src="../static/add-o.png" />
      添加商户
    </view>
  </view>
  <view class="list-box">
    <block wx:for="{{merchantList}}" wx:key="index">
      <view class="merchant-item" data-item="{{item}}" bind:tap="handleSwitchMerchant">
        <view class="content-box">
          <view class="row">
            <view class="icon {{item.authIconClass}}"></view>
            <view class="name">{{item.realName}}</view>
          </view>
          <block wx:if="{{item.openService}}">
            <view class="row">
              <block wx:for="{{item.openService}}" wx:key="serviceId">
                <view class="label">{{item.serviceName}}</view>
              </block>
            </view>
          </block>
          <block wx:else>
            <view class="row">
              <view class="tips">暂未开通业务</view>
            </view>
          </block>
        </view>
        <image wx:if="{{item.currentActive}}" class="active-icon" src="../static/check-red.png"></image>
      </view>
    </block>
  </view>
</van-popup>