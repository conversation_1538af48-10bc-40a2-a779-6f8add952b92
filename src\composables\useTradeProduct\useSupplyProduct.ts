import type { MatchTypes } from "@/api";
import { TradeProductType } from "@/enums";
import { omit } from "lodash";

export function useSupplyProduct(data: MatchTypes.SupplyProductAppRsp) {
  // 转换成通用商品类型
  const tradeProduct: MatchTypes.ProductAppRsp = {
    ...omit(data, ["tag"]),
    tagList: data.tag,
    supplyImageMain: data.supplyImageMain?.fileKey,
    productType: {
      code: TradeProductType.supply,
    },
  };
  return useTradeProduct(tradeProduct);
}
