<template>
  <nut-tabbar
    bottom
    safe-area-inset-bottom
    :active-color="color.active"
    :unactive-color="color.unactive"
    @tab-switch="(_, index) => select(Number(index))"
  >
    <nut-tabbar-item
      v-for="tab in tabs"
      :key="tab.value"
      :dot="unReadCount > 0 && tab.value === NavigationTabEnum.Message"
      custom-color="#FF5E5E"
    >
      <div :class="value === tab.value ? 'opacity-100' : 'opacity-50'">
        <div class="relative h-50rpx w-50rpx">
          <image :src="tab.icon" class="absolute left-0 z-1 size-[50rpx]" />
          <image
            v-if="value === tab.value"
            :src="formatIcon(tab.activeIcon)"
            class="absolute left-0 z-2 size-[50rpx]"
          />
        </div>
        <div
          class="text-22rpx leading-[1.5]"
          :class="value === tab.value ? 'text-#0591db' : ''"
        >
          {{ tab.name }}
        </div>
      </div>
    </nut-tabbar-item>
  </nut-tabbar>
</template>

<script setup lang="ts">
import Center from "@/static/tabbar/center.png";
import Discover from "@/static/tabbar/discover.png";
import Home from "@/static/tabbar/home.png";
import Notice from "@/static/tabbar/notice.png";
import { NavigationTabEnum } from "../type";

defineOptions({
  options: { styleIsolation: "shared" },
});

const emit = defineEmits<{ change: [value: NavigationTabEnum] }>();
const { withLogin } = useNavigate();
const value = defineModel<NavigationTabEnum>({ required: true });
const { unReadCount } = storeToRefs(useUserStore());
const color = {
  active: "inherit",
  unactive: "inherit",
};
const tabs = ref([
  {
    icon: Home,
    activeIcon: "home_on.gif".toQiniuUrl(),
    name: t("shou-ye"),
    value: NavigationTabEnum.Home,
  },
  {
    icon: Discover,
    activeIcon: "discover_on.gif".toQiniuUrl(),
    name: t("qing-bao"),
    value: NavigationTabEnum.Report,
  },
  {
    icon: Notice,
    activeIcon: "notice_on.gif".toQiniuUrl(),
    name: t("xiao-xi"),
    shouldLogin: true,
    value: NavigationTabEnum.Message,
  },
  {
    icon: Center,
    activeIcon: "center_on.gif".toQiniuUrl(),
    name: t("wo-de"),
    shouldLogin: true,
    value: NavigationTabEnum.Mine,
  },
]);

function formatIcon(icon: string) {
  return `${icon}?${new Date().getTime()}`;
}

function select(index: number) {
  const tabValue = tabs.value[index];

  withLogin({
    when: () => tabValue.shouldLogin === true,
  }).run(() => {
    if (value.value !== tabValue.value) {
      value.value = tabValue.value;
      emit("change", tabValue.value);
    }
  });
}
</script>

<style lang="scss">
.nut-tabbar {
  border-top: none !important;
  border-bottom: none !important;
  box-shadow: 0rpx -4rpx 8rpx 0rpx rgb(10 25 40 / 3%) !important;
}
</style>
