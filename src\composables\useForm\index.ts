import type { FormItemRule, InputType } from "nutui-uniapp";

export * as FormRules from "./form.rules";

interface PresetFormItem<T = any> {
  value?: T;
  rules?: FormItemRule[];
  placeholder?: string;
  disabled?: boolean;
  label?: string;
  clearable?: boolean;
  type?: InputType | "textarea";
  maxLength?: number;
  autofocus?: boolean;
}

export type CollectFormItem = {
  key: string;
  placeholder?: string;
} & Omit<PresetFormItem, "value" | "rules">;

type AnyDefineForm = Record<string, PresetFormItem>;

type LimitedDefineForm<T> = {
  [K in keyof T]?: PresetFormItem<T[K]>;
} & AnyDefineForm;

type CollectItem<T, P> = {
  [K in keyof T]: P;
};

type ModelValue = string | number | boolean | object;

type CollectFormData<T> = {
  [K in keyof T]: T[K] extends PresetFormItem<infer V>
    ? V extends ModelValue
      ? V | undefined
      : any
    : T extends object
    ? T[K]
    : any;
};

export function useForm<T>(
  options: T extends AnyDefineForm ? T : LimitedDefineForm<T>,
) {
  const collectForm = {} as CollectFormData<T>;
  const collectConfig = {} as CollectItem<T, CollectFormItem>;
  const collectRules = {} as CollectItem<T, FormItemRule[]>;

  for (const _ in options) {
    const key = _ as keyof T;
    const presetForm = options[key];
    const label = presetForm.label || "";
    collectRules[key] = presetForm.rules || [];
    collectForm[key] = presetForm.value;
    collectConfig[key] = {
      key: key as string,
      label,
      type: presetForm.type ?? "text",
      clearable: presetForm.clearable ?? true,
      maxLength: presetForm.maxLength,
      placeholder: presetForm.placeholder ?? t("qing-shu-ru-label", [label]),
      disabled: presetForm.disabled,
      autofocus: presetForm.autofocus ?? false,
    };
  }

  const formData = reactive(collectForm);
  const formConfig = reactive(collectConfig);

  return {
    formData,
    formConfig,
    rules: collectRules,
  };
}
