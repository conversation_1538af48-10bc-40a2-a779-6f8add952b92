<template>
  <merchant-popup
    :show="merchantPopupVisible"
    :list="merchantList"
    @switch="handleSwitchMerchant"
    @close="handleClose"
    @add="handleAddMerchant" />
</template>

<script lang="ts">
import Vue from "vue";
export default class extends Vue {
  merchantPopupVisible = false;
  merchantList = [];
  handleClose() {
    this.merchantPopupVisible = false;
  }
  handleSwitchMerchant() {
    // 需要手动触发弹窗关闭
    this.handleClose();
  }
  handleAddMerchant() {}
}
</script>
