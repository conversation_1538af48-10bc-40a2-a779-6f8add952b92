<template>
  <div class="mt-20rpx flex flex-col gap-16rpx">
    <div v-for="item in attrs" :key="item[0]">
      <ProductAttribute :title="item[0]">
        {{ item[1] || "--" }}
      </ProductAttribute>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { MatchTypes } from "@/api";
import ProductAttribute from "@/components/Trade/ProductAttribute.vue";
import { ProductSourceType } from "@/enums";
import { formatYM } from "@/utils/date";

const { data } = defineProps<{
  data: MatchTypes.PurchaseProductAppRsp | MatchTypes.SupplyProductAppRsp;
}>();

function slashJoin(strs: Array<string | undefined>) {
  return strs.filter(str => str).join(" / ");
}

const attrs = computed(() => {
  let attributes: Array<[string, string | undefined]> = [];
  if (data) {
    attributes.push([
      t("pin-lei"),
      slashJoin([
        data.firstLevelName,
        data.secondLevelName,
        data.threeLevelsName,
      ]),
    ]);
  }

  if (data.source?.code === ProductSourceType.IMPORT_GOODS) {
    attributes = attributes.concat([
      [t("chan-di"), data.originCountry],
      [t("chang-hao"), data.brand],
    ]);
  } else if (data.source?.code === ProductSourceType.DOMESTIC_GOODS) {
    attributes = attributes.concat([
      [t("pin-pai"), data.brandName],
      [t("sheng-chan-shang"), data.manufacturer],
    ]);
  }

  if (isSupplyType(data)) {
    attributes.push([
      t("fa-huo-di"),
      slashJoin([
        data.deliveryProvince,
        data.deliveryCity,
        data.deliveryRegion,
      ]),
    ]);
  }

  if (isPurchaseType(data)) {
    attributes.push([t("shou-huo-di"), data.receiveCity]);
  }

  attributes.push([t("sheng-chan-ri-qi"), formatYM(data.productionTime)]);

  return attributes;
});
</script>
