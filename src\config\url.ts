import { ftPlugin } from "oig-finclip-jssdk/weapp";

export class BaseUrlConfig {
  private test = "https://wxapp.oigit.cn";
  private prod = "https://weapp.youdingte.com";
  public current = "";

  constructor() {
    this.init();
  }

  async init() {
    if (ftPlugin.inFinClip ?? false) {
      // APP 环境
      const { env } = await ftPlugin?.getNativeEnv();
      if (env === "test") {
        this.current = this.test;
      } else if (env === "production") {
        this.current = this.prod;
      }
    } else {
      // 微信环境
      const env = uni.getAccountInfoSync().miniProgram.envVersion;
      this.current = env === "release" ? this.prod : this.test;
    }
  }
}

export const baseUrlConfig = new BaseUrlConfig();

export class QiniuConfig {
  private test = "https://qiniu-test.youdingte.com";
  private prod = "https://qiniu.youdingte.com";
  public current = "";

  constructor() {
    const env = uni.getAccountInfoSync().miniProgram.envVersion;
    this.current = env === "release" ? this.prod : this.test;
  }
}

export class ChatBaseUrlConfig {
  // private test = "http://***************:8900";
  // private test = "https://ai-agent-dev.ksout.oigit.com";
  private test = "https://wxapp.oigit.cn";
  private prod = "https://weapp.youdingte.com";
  public current = "";

  constructor() {
    this.init();
  }

  async init() {
    if (ftPlugin.inFinClip ?? false) {
      // APP 环境
      const { env } = await ftPlugin?.getNativeEnv();
      if (env === "test") {
        this.current = this.test;
      } else if (env === "production") {
        this.current = this.prod;
      }
    } else {
      // 微信环境
      const env = uni.getAccountInfoSync().miniProgram.envVersion;
      this.current = env === "release" ? this.prod : this.test;
    }
  }
}

export const chatBaseUrlConfig = new ChatBaseUrlConfig();
