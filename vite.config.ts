import type { ConfigEnv, UserConfig } from "vite";
import process from "node:process";
import { defineConfig, loadEnv } from "vite";
import { setupVitePlugins } from "./build/plugins";
import { convertEnv, getRootPath, getSrcPath } from "./build/utils";

// https://vitejs.dev/config/
export default defineConfig(
  async (configEnv: ConfigEnv): Promise<UserConfig> => {
    const srcPath = getSrcPath();
    const rootPath = getRootPath();
    const viteEnv = convertEnv(loadEnv(configEnv.mode, process.cwd()));
    const { VITE_PORT } = viteEnv;

    return {
      plugins: [...(await setupVitePlugins())],
      server: {
        host: "0.0.0.0",
        port: VITE_PORT,
        open: false,
      },
      envPrefix: ["VITE_", "UNI_"],
      build: {
        target: "es6",
        cssTarget: "chrome61",
        reportCompressedSize: false,
        sourcemap: false,
        chunkSizeWarningLimit: 1024,
        commonjsOptions: {
          ignoreTryCatch: false,
        },
      },
      css: {
        preprocessorOptions: {
          scss: {
            api: "modern-compiler",
            additionalData: "@import '@/styles/variables.scss';",
          },
        },
      },
      resolve: {
        alias: {
          "~": rootPath,
          "@": srcPath,
        },
      },
    };
  },
);
