# 流式数据解析器使用说明

## 概述

这个流式数据解析器专门用于处理跨chunk的JSON对象，能够自动检测数据完整性，无需手动指定是否为最后一个数据块。

## 主要特性

### ✅ 自动完整性检测
- 不再需要手动指定 `isComplete` 参数
- 自动分析括号匹配、字符串状态等来判断数据是否完整
- 智能处理跨chunk的JSON对象

### ✅ 跨chunk数据处理
- 支持JSON对象被分割到多个chunk的情况
- 自动识别没有 `data:` 前缀的续传数据
- 正确拼接和解析完整的JSON对象

### ✅ 多格式支持
- 支持标准JSON格式
- 支持JavaScript对象字面量格式（属性名不带引号）
- 自动尝试不同的解析方式

### ✅ 错误恢复
- 解析失败时能够跳过错误数据继续处理
- 详细的错误日志帮助调试
- 智能的缓存管理

## 使用方法

### 基本用法

```typescript
import { parseStreamingData } from './useParseStreaming';

// 自动检测完整性，无需指定isComplete
const result = parseStreamingData(chunkData);
console.log(`解析到 ${result.length} 个对象`);
```

### 在adapter中的使用

```typescript
// src/api/adapter.ts
const parseData: ChunkData[] = parseStreamingData(decodeData); // 自动检测完整性
parseData.forEach(item => {
  // 处理每个解析出的对象
  const response: StreamResponse = {
    data: item,
    status: "streaming",
  };
  // 调用transformResponse处理
  config.transformResponse?.(response);
});
```

## 解决的问题

### 问题场景
你遇到的原始问题：
```
chunk1: data: { message: { ... action_content: [{ orderId: "xxx", stockPiece: 924,
chunk2:    stockGrossWeight: "20584.626", ... }, { orderId: "xxx2", ...
chunk3:  factoryCode: "SIF2146", ... }], total: 300, ... } data: { message: { ... } }
```

### 解决方案
1. **chunk1**: 包含不完整的JSON对象 → 缓存数据，等待更多chunk
2. **chunk2**: 续传数据，没有`data:`前缀 → 自动识别并拼接到缓存
3. **chunk3**: 完成第一个JSON对象并包含第二个完整对象 → 解析出2个对象

### 结果
- ✅ 正确解析出2个完整的对象
- ✅ 第一个对象包含完整的 `action_content` 数组（2个元素）
- ✅ 第二个对象是简化版本（不包含 `action_content`）

## API 参考

### parseStreamingData(data, isComplete?)

**参数:**
- `data: string` - 流式数据chunk
- `isComplete?: boolean` - 可选，是否为最后一个chunk（默认自动检测）

**返回值:**
- `ChunkData[]` - 解析出的JSON对象数组

**示例:**
```typescript
// 自动检测（推荐）
const result = parseStreamingData(chunk);

// 手动指定（如果确定是最后一个chunk）
const result = parseStreamingData(chunk, true);
```

### resetStreamingParser()

重置解析器的内部缓存，在开始新的流式数据解析时调用。

```typescript
import { resetStreamingParser } from './useParseStreaming';

// 开始新的对话时重置
resetStreamingParser();
```

## 调试工具

### 调试解析器
```typescript
import { debugStreamingParser } from './debug-streaming-parser';

// 调试多个chunk的解析过程
const chunks = [chunk1, chunk2, chunk3];
const results = debugStreamingParser(chunks);
```

### 获取缓存信息
```typescript
import { getBufferLength } from './useParseStreaming';

console.log('当前缓存长度:', getBufferLength());
```

## 注意事项

1. **安全性**: 解析器使用 `eval()` 来处理JavaScript对象字面量格式，请确保数据来源可信
2. **性能**: 大量数据时建议分批处理，避免内存占用过高
3. **错误处理**: 解析失败的数据会被跳过，请检查控制台警告信息
4. **缓存管理**: 解析器会自动管理内部缓存，无需手动清理

## 更新日志

### v2.0.0 (当前版本)
- ✅ 新增自动完整性检测功能
- ✅ 改进跨chunk数据处理逻辑
- ✅ 支持JavaScript对象字面量格式
- ✅ 优化错误恢复机制
- ✅ 简化API，移除必需的isComplete参数

### v1.0.0
- 基础的流式数据解析功能
- 需要手动指定isComplete参数
