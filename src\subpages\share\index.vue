<!-- 受邀人扫码后展示的邀请页面 -->
<template>
  <div />
</template>

<script setup lang="ts">
const { userInfo } = storeToRefs(useUserStore());
const { openWebview, withLogin, redirectToIndex } = useNavigate();
const ticket = ref("");

onMounted(() => {
  if (!ticket.value) {
    return handleBack();
  }
  withLogin({ returnTo: true }).run(() => {
    fetchUrl();
  });
});

onLoad(query => {
  ticket.value = query?.ticket ?? "";
});

function handleBack() {
  uni.showModal({
    content: "未查询到邀请信息，请稍后再试",
    showCancel: false,
    success: () => {
      redirectToIndex();
    },
  });
}

async function fetchUrl() {
  try {
    const rsp = await userApi.ApiUserInviteDecodeticketPost({
      ticket: ticket.value,
    });

    if (rsp?.data?.inviteResult === "SUCCESS") {
      return uni.redirectTo({ url: "/subpages/share/success" });
    }

    let url = rsp?.data?.shareUrl ?? "";
    if (userInfo.value?.mobileNum) {
      url = url.toParamsUrl({ phone: userInfo.value?.mobileNum });
    }
    openWebview({ url, isRedirect: true });
  } catch {
    handleBack();
  }
}
</script>
