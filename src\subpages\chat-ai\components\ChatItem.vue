<template>
  <div
    class="mb-20rpx flex"
    :class="isUser ? 'flex-row-reverse px-30rpx' : 'flex-row'"
  >
    <div class="w-full flex-col">
      <div class="px-30rpx">
        <div
          v-if="!isUser"
          class="mb-20rpx h-76rpx w-76rpx flex items-center self-start justify-center rounded-full bg-#F4F5F7"
        >
          <image
            :src="LogoIcon"
            mode="scaleToFill"
            class="block h-38rpx w-38rpx"
          />
        </div>
      </div>
      <div v-if="isUser" class="flex justify-end">
        <div
          v-if="message.file"
          class="mb-20rpx w-40vw flex overflow-hidden rounded-20rpx"
        >
          <image
            :src="message.file"
            mode="widthFix"
            class="max-w-full"
            @click="handlePreview(message.file)"
          />
        </div>
        <div
          class="relative box-border w-fit flex flex-col justify-end rounded-[24rpx_0rpx_24rpx_24rpx] bg-#DDF0FF px-30rpx c-#394253"
        >
          <div
            v-if="message.content"
            class="select-text break-words py-18rpx text-30rpx lh-44rpx"
            :style="{
              wordBreak: 'break-word',
            }"
          >
            <MpHtml
              :content="message.content"
              :markdown="true"
              :selectable="true"
              class="break-words"
            />
          </div>
        </div>
      </div>
      <div v-else class="w-full">
        <template v-for="(item, index) in message.contentBlocks" :key="index">
          <div v-if="item.type === ContentBlockTypeEnum.think" class="px-30rpx">
            <TypingBubble
              :text="item.content || ''"
              :title="item.isThink ? '已深度思考' : '正在分析...'"
            />
          </div>
          <div
            v-else-if="item.type === ContentBlockTypeEnum.content"
            class="relative box-border w-full px-30rpx c-black"
          >
            <div
              v-if="item.content"
              class="select-text break-words pb-18rpx text-30rpx lh-44rpx"
              :style="{
                wordBreak: 'break-word',
              }"
            >
              <MpHtml
                :content="item.content"
                :markdown="true"
                :selectable="true"
              />
            </div>
          </div>
          <div
            v-else-if="item.type === ContentBlockTypeEnum.workflow"
            class="relative box-border w-full flex flex-col justify-end px-0 c-black!"
          >
            <template
              v-for="(nodeItem, nodeIndex) in item.workflows.nodes"
              :key="nodeIndex"
            >
              <Card
                v-if="nodeItem.is_card && nodeItem.cardList?.length"
                :card-list="nodeItem.cardList"
                :card-type="nodeItem.card_type"
                :search-key="nodeItem.ai_search_key"
              />
              <div
                v-else-if="!nodeItem.is_card && nodeItem.actionContent"
                class="select-text break-words px-30rpx py-18rpx text-30rpx lh-44rpx"
                :style="{
                  wordBreak: 'break-word',
                }"
              >
                <MpHtml
                  :content="nodeItem.actionContent"
                  :markdown="true"
                  :selectable="true"
                />
              </div>
            </template>
          </div>
        </template>
        <div v-if="isLoading" class="flex items-center gap-x-10rpx px-30rpx">
          <nut-icon name="loading1" />
          <div class="text-28rpx">正在思考中...</div>
        </div>
        <div v-else-if="isError" class="flex gap-x-10rpx px-30rpx">
          <nut-icon name="ask2" class="h-full" />
          <div class="text-28rpx text-red-700">{{ message.content }}</div>
        </div>
        <div v-if="isShowTips" class="flex justify-between px-30rpx py-28rpx">
          <div class="flex items-center gap-x-20rpx">
            <image
              :src="CopyIcon"
              class="h-68rpx w-68rpx"
              @click="handleCopy"
            />
            <image
              :src="curLikeIcon"
              class="h-68rpx w-68rpx"
              @click="handleLike"
            />
            <image
              :src="curDislikeIcon"
              class="h-68rpx w-68rpx"
              @click="handleDislike"
            />
          </div>
        </div>
      </div>
    </div>
    <Feedback
      v-model="showFeedback"
      :intention-id="message.intention_id"
      @feedback="onFeedback"
    />
  </div>
</template>

<script setup lang="ts">
import type { ChatTypes } from "@/api";
import {
  ContentBlockTypeEnum,
  FeedbackStatus,
  MessageStatusEnum,
  RoleEnum,
} from "@/enums/chat";
import CopyIcon from "@/static/chat/copy-icon.png";
import DislikeActiveIcon from "@/static/chat/dislike-active-icon.png";
import DislikeIcon from "@/static/chat/dislike-icon.png";
import LikeActiveIcon from "@/static/chat/like-active-icon.png";
import LikeIcon from "@/static/chat/like-icon.png";
import LogoIcon from "@/static/logo.png";
import MpHtml from "@/subpages/components/MpHtml2/components/mp-html/mp-html.vue";
import Card from "./cards/index.vue";
import Feedback from "./Feedback.vue";
import TypingBubble from "./TypingBubble.vue";

const { message } = defineProps<{
  message: ChatTypes.ChatMessage;
}>();
const emit = defineEmits<{
  (e: "update-message", message: ChatTypes.ChatMessage): void;
}>();

const isUser = computed(() => message.role === RoleEnum.user_message);
const isAssistant = computed(() => message.role === RoleEnum.Assistant);
// const isSystem = computed(() => message.role === RoleEnum.System);
const isLoading = computed(() => message.status === MessageStatusEnum.Loading);
const isError = computed(() => message.status === MessageStatusEnum.Error);
const isShowTips = computed(
  () =>
    message.status === MessageStatusEnum.Finish &&
    !message.error &&
    isAssistant.value,
);

// 点赞状态
const curLikeIcon = computed(() =>
  message.feedback === FeedbackStatus.Good ? LikeActiveIcon : LikeIcon,
);
const curDislikeIcon = computed(() =>
  message.feedback === FeedbackStatus.Bad ? DislikeActiveIcon : DislikeIcon,
);

// 点赞
const showFeedback = ref<boolean>(false);
const isFinished = ref<boolean>(false);
async function handleLike() {
  try {
    if (isFinished.value) return;
    isFinished.value = true;
    if (message.feedback === FeedbackStatus.Good) {
      await chatApi.fetchFeedback({
        is_good: FeedbackStatus.Normal,
        intention_id: message.intention_id,
      });
      emit("update-message", {
        ...message,
        feedback: FeedbackStatus.Normal,
      });
    } else {
      await chatApi.fetchFeedback({
        is_good: FeedbackStatus.Good,
        intention_id: message.intention_id,
      });
      emit("update-message", {
        ...message,
        feedback: FeedbackStatus.Good,
      });
    }
  } catch (err) {
    console.error(err);
  } finally {
    isFinished.value = false;
  }
}
// 点踩
async function handleDislike() {
  if (message.feedback === FeedbackStatus.Bad) {
    await chatApi.fetchFeedback({
      is_good: FeedbackStatus.Normal,
      intention_id: message.intention_id,
    });
    emit("update-message", {
      ...message,
      feedback: FeedbackStatus.Normal,
    });
  } else {
    showFeedback.value = true;
  }
}

// 反馈成功改变状态
function onFeedback() {
  emit("update-message", {
    ...message,
    feedback: FeedbackStatus.Bad,
  });
}

function handlePreview(url: string) {
  uni.previewImage({
    urls: [url],
  });
}

function handleCopy() {
  let data = "";
  message.contentBlocks?.forEach(item => {
    data += item.content;
    item.workflows.nodes.forEach(nodeItem => {
      if (!nodeItem.is_card && nodeItem.actionContent) {
        data += nodeItem.actionContent;
      }
    });
  });
  uni.setClipboardData({
    data,
    success: () => showMsg("复制成功"),
    fail: err => {
      console.error("setClipboardData", err);
    },
  });
}
</script>
