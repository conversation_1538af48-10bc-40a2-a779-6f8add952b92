<template>
  <view class="box" @click="emit('click')">
    <view class="mb-34rpx flex items-center justify-between">
      <view class="h-36rpx text-26rpx c-#33373F font-400 lh-36rpx">
        {{ data.contract }} / {{ data.cabinetNo }}
      </view>
      <view class="flex items-center justify-center gap-x-8rpx">
        <view :class="curLabel.calssName">
          {{ curLabel.label }}
        </view>
        <!-- <view class="plain-blue-label">已入库</view> -->
      </view>
    </view>
    <view class="flex flex-wrap text-[24rpx] text-[#878c94] fw-400">
      <view
        v-for="(item, index) in dataList"
        :key="index"
        class="mb-24rpx w-33.3%"
        :style="{ 'text-align': item.align }"
      >
        <view class="label">{{ item.label }}</view>
        <view
          class="mt-6rpx break-words text-[#333]"
          :class="{ 'text-[#fd5555]': item?.red }"
        >
          {{ item.value }}
        </view>
      </view>
    </view>
    <view class="line" style="margin-top: 0" />

    <view class="goods-name">
      {{ `${data.countryName} | ${data.factoryCode} | ${data.goodsName}` }}
    </view>
  </view>
</template>

<script lang="ts" setup>
import { OrderStatusEnum } from "@/enums/chat";

interface List {
  label: string;
  value: string | undefined | number;
  align: "left" | "right" | "center";
  red?: boolean;
}

const props = withDefaults(defineProps<{ data: any }>(), {
  data: () => ({}),
});

const emit = defineEmits(["click"]);

const dataList = computed((): List[] => {
  return [
    {
      label: "合同金额",
      value: props.data.allAmount,
      align: "left",
    },
    {
      label: "ETA",
      value: props.data.finalVoyageEta,
      align: "center",
    },
    { label: "目的港", value: props.data.portName, align: "right" },
  ];
});

// 订单状态
const O_OrderStatusEnum = {
  [OrderStatusEnum.All]: {
    label: "全部",
    calssName: "disable-label",
  },
  [OrderStatusEnum.A00]: {
    label: "未开始",
    calssName: "disable-label",
  },
  [OrderStatusEnum.B00]: {
    label: "未到港",
    calssName: "disable-label",
  },
  [OrderStatusEnum.C10]: {
    label: "清关中",
    calssName: "disable-label",
  },
  [OrderStatusEnum.C20]: {
    label: "清关完成",
    calssName: "plain-blue-label",
  },
  [OrderStatusEnum.C30]: {
    label: "已入库",
    calssName: "plain-blue-label",
  },
  [OrderStatusEnum.C40]: {
    label: "已出清",
    calssName: "plain-blue-label",
  },
  [OrderStatusEnum.C50]: {
    label: "已完成",
    calssName: "plain-blue-label",
  },
};

const curLabel = computed(() => {
  return O_OrderStatusEnum[props.data.orderStatus as OrderStatusEnum];
});
</script>

<style lang="scss" scoped>
.box {
  padding: 34rpx 24rpx;
  background: #fff;
  border-radius: 16rpx;

  .top {
    display: flex;
    justify-content: space-between;
    margin-bottom: 34rpx;

    .right {
      display: flex;
      align-items: center;
      width: 136rpx;
      height: 52rpx;
      padding: 6rpx;
      font-size: 26rpx;
      font-weight: 400;
      line-height: 52rpx;
      color: #1478f0;
      border: 2rpx solid #deefff;
      border-radius: 32rpx;

      .add {
        width: 40rpx;
        height: 40rpx;
        margin-right: 8rpx;
        line-height: 40rpx;
        text-align: center;
        background: #deefff;
        border-radius: 50%;
      }
    }
  }

  .line {
    width: 100%;
    height: 2rpx;
    margin: 24rpx 0;
    background-image: linear-gradient(to right, #b8b9ba 50%, white 50%);
    background-size: 24rpx 2rpx;
  }

  .goods-name {
    display: -webkit-box;
    overflow: hidden;
    font-size: 30rpx;
    font-weight: 600;
    line-height: 42rpx;
    color: #33373f;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2; /* 显示的行数 */
    word-break: break-all;
    -webkit-box-orient: vertical; /* 垂直方向上的盒子 */
  }
}
</style>
