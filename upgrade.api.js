const { execSync } = require("node:child_process");

const apis = [
  ["youxian-api", "git+http://192.168.186.118/FRONT/youxian-api-render.git"],
  [
    "matchmaking-api",
    "git+http://192.168.186.118/FRONT/matchmaking-api-render.git",
  ],

  ["news-api", "git+http://192.168.186.118/FRONT/new-center-api-render"],
  ["worldrou-api", "git+http://192.168.186.118/FRONT/worldrou-api-render.git"],
];

(function () {
  try {
    console.log("正在清理Api模块...");
    execSync(`pnpm uninstall ${apis.map(item => item[0]).join(" ")}`);
  } catch {}

  console.log("Api模块清理完成");

  execSync(
    `pnpm install ${apis
      .map(item => `${item[0]}@${item[1]}`)
      .join(" ")} --force`,
    {
      stdio: "inherit",
    },
  );
})();
