<template>
  <NoData v-if="empty || state.loading" :loading="state.loading" />
  <scroll-view
    v-else
    scroll-y
    :style="{ height: scrollViewHeight }"
    :refresher-enabled="refresherEnabled"
    :lower-threshold="50"
    :refresher-triggered="state.refreshing"
    @scrolltolower="emit('onLoadMore')"
    @refresherrefresh="emit('onRefresh')"
  >
    <slot />
  </scroll-view>
  <uni-load-more :status="state.loadMoreStatus" :content-text="contentText" />
</template>

<script setup lang="ts">
defineProps<{
  empty?: boolean;
  state: ListFetchState;
  refresherEnabled?: boolean;
  scrollViewHeight?: string;
}>();
const emit = defineEmits<{ onLoadMore: []; onRefresh: [] }>();

const contentText = ref({
  contentdown: " ",
  contentrefresh: "加载中...",
  contentnomore: " ",
});
</script>
