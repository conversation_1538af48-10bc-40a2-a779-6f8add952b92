<template>
  <GridMenuSkeleton v-if="loading && gridAppList.length === 0" :cols="cols" />
  <GridMenu :grid-app-list="gridAppList">
    <template #default="{ item }">
      <image :src="item.imageUrl" class="mt-20rpx h-[66rpx] w-[64rpx]" />
      <view
        class="mt-10rpx w-[100%] truncate text-center text-[22rpx] font-400"
      >
        {{ item.functionName }}
      </view>
    </template>
  </GridMenu>
</template>

<script setup lang="ts">
import type { PlatformTypes } from "@/api";
import { GridAppPosition, GridAppType } from "@/enums";
import GridMenu from "../../components/GridMenu.vue";
import GridMenuSkeleton from "./GridMenuSkeleton.vue";

defineOptions({
  options: { styleIsolation: "shared" },
});

const KEY_HOME_GRID = "homeGrid";

const cols = 5;
const rows = 2;
const size = cols * rows;

const gridAppList = ref<PlatformTypes.IAppFunctionByTypeVo[]>([]);

// eslint-disable-next-line prefer-const
let [fetchGridAppList, loading] = useApiFetch(() =>
  platformApi
    .ApiAppnewFunctionsGet({
      clientType: GridAppType.miniProgram,
      showPosition: GridAppPosition.store,
    })
    .then(async rsp => {
      gridAppList.value = rsp.data;
      uni.setStorageSync(KEY_HOME_GRID, JSON.stringify(gridAppList.value));
      return rsp;
    }),
);

function tryInitStroage(): PlatformTypes.IPlatformFrontAppItemVo[] {
  const storageData = uni.getStorageSync(KEY_HOME_GRID);
  if (storageData) {
    try {
      gridAppList.value = JSON.parse(storageData);
    } catch {}
  }

  return [];
}

onBeforeMount(() => {
  tryInitStroage();
  fetchGridAppList();
});

defineExpose({
  fetchGridAppList,
});
</script>

<style lang="scss" scoped>
::v-deep .nut-swiper-pagination {
  bottom: 0;
}
</style>
