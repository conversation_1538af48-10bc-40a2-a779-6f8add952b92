<template>
  <nut-popup
    v-model:visible="showFeedback"
    position="center"
    round
    :safe-area-inset-bottom="true"
    :custom-style="{ width: '90%' }"
    :close-on-click-overlay="false"
  >
    <div class="px-30rpx pb-46rpx pt-36rpx">
      <div class="relative mb-30rpx flex items-center gap-x-12rpx">
        <image
          :src="DislikeBlueIcon"
          mode="scaleToFill"
          class="block h-64rpx w-64rpx"
        />
        <div class="h-56rpx text-40rpx c-#153147 font-500 lh-56rpx">
          您的反馈将帮助我们进步
        </div>
        <image
          :src="CloseIcon"
          mode="scaleToFill"
          class="absolute right-0 top-0 block h-40rpx w-40rpx"
          @click="handleClose"
        />
      </div>

      <div
        v-if="list.length"
        class="mb-32rpx flex flex-wrap gap-16rpx text-26rpx lh-36rpx"
      >
        <div
          v-for="(item, index) in list"
          :key="index"
          class="h-36rpx rounded-30rpx bg-#F4F5F7 px-26rpx py-12rpx c-#153147"
          @click="handleAppendTip(item.text)"
        >
          {{ item.text }}
        </div>
      </div>
      <div class="h-200rpx overflow-hidden rounded-12rpx">
        <nut-textarea
          v-model="feedbackContent"
          placeholder="请留下你宝贵的建议和反馈"
          cursor-color="#078EF8"
          auto-focus
          :cursor-spacing="150"
          :max-length="200"
          limit-show
          class="nut-text"
          @input="onChange"
        />
      </div>
      <nut-button
        :loading="loading"
        type="primary"
        custom-color="#0591DB"
        @click="handleSubmit"
      >
        提 交
      </nut-button>
    </div>
  </nut-popup>
</template>

<script setup lang="ts">
import { FeedbackStatus } from "@/enums/chat";
import CloseIcon from "@/static/chat/close-icon.png";
import DislikeBlueIcon from "@/static/chat/dislike-blue.png";

defineOptions({
  options: { styleIsolation: "shared" },
});

const props = defineProps<{
  intentionId: number;
}>();
const emit = defineEmits<{
  (e: "feedback"): void;
}>();

const showFeedback = defineModel<boolean>({ required: true });

const list = ref<any>([
  // {
  //   text: "查询我的订单",
  // },
  // {
  //   text: "查船期",
  // },
  // {
  //   text: "查船期",
  // },
  // {
  //   text: "查询我的订单",
  // },
  // {
  //   text: "查船期",
  // },
  // {
  //   text: "查船期",
  // },
]);

const feedbackContent = ref("");
function onChange(val: string) {
  console.log(val);
}

function handleAppendTip(tip: string) {
  feedbackContent.value += `${tip}；`;
}

const loading = ref<boolean>(false);
async function handleSubmit() {
  try {
    if (!feedbackContent.value.trim()) {
      showMsg("请输入反馈内容");
      return;
    }
    loading.value = true;
    await chatApi.fetchFeedback({
      is_good: FeedbackStatus.Bad,
      intention_id: props.intentionId,
      user_comment: feedbackContent.value,
    });
    emit("feedback");
    showMsg("提交成功");
    handleClose();
  } catch (err) {
    console.log(err);
  } finally {
    loading.value = false;
  }
}

function handleClose() {
  showFeedback.value = false;
}
</script>

<style lang="scss" scoped>
::v-deep .nut-button {
  width: 100%;
  height: 88rpx;
  margin-top: 36rpx;
  font-size: 32rpx;
  font-weight: 500rpx;
  border-radius: 8rpx;
}

::v-deep .nut-textarea {
  @apply box-border h-full w-full p-16rpx text-28rpx c-#33373F lh-40rpx bg-#F4F5F7;

  .nut-textarea__textarea {
    height: 100%;
  }
}
</style>
