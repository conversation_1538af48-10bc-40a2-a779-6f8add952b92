<template>
  <div
    class="f-c gap-10rpx rounded-4rpx bg-blue-light px-16rpx py-6rpx text-24rpx color-#153147 leading-[32rpx]"
    @click="trigger"
  >
    <nut-icon v-if="item.isDateTag" name="tips" />
    <span> {{ item.text }}</span>
  </div>
</template>

<script setup lang="ts">
import type { TradeTag } from "@/enums";

defineOptions({
  options: { styleIsolation: "shared" },
});

const props = defineProps<{ item: TradeTag }>();

const emit = defineEmits<{ trigger: [value: TradeTag] }>();

function trigger() {
  if (props.item.isDateTag) {
    emit("trigger", props.item);
  }
}
</script>

<style lang="scss" scoped>
::v-deep .nut-icon {
  width: 10px !important;
  height: 10px !important;
  font-size: 11px !important;
}
</style>
