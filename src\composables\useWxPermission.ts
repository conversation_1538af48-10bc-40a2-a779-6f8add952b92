export function useWxPermission() {
  // 获取定位
  async function getLocation(type: string): Promise<UniApp.GetLocationSuccess> {
    return new Promise(resolve => {
      uni.getLocation({
        type,
        success(result) {
          resolve(result);
        },
        fail(e) {
          if (
            [
              "getLocation:fail:auth denied",
              "getLocation:fail auth deny",
              "getLocation:fail authorize no response",
            ].includes(e.errMsg)
          ) {
            showConfirm("定位权限未开启，是否跳转到设置页并授权位置信息？", {
              confirmText: "去授权",
            }).then(() => {
              uni.openSetting();
            });
          }
        },
      });
    });
  }

  async function getCamera() {
    return new Promise((resolve, reject) => {
      uni.authorize({
        scope: "scope.camera",
        success() {
          resolve(true);
        },
        fail(e) {
          if (
            [
              "authorize:fail auth deny",
              "authorize:fail authorize no response",
            ].includes(e.errMsg)
          ) {
            showConfirm("相机权限未开启，是否跳转到设置页并授权相机？", {
              confirmText: "去授权",
            }).then(() => {
              uni.openSetting();
            });
          } else {
            reject(e);
          }
        },
      });
    });
  }

  return {
    getLocation,
    getCamera,
  };
}
