/* eslint-disable */
var object = require('./object.wxs');
var array = require('./array.wxs');

function style(styles) {
  if (array.isArray(styles)) {
    return styles
      .filter(function (item) {
        return item != null;
      })
      .map(function (item) {
        return style(item);
      })
      .join(';');
  }

  if ('Object' === styles.constructor) {
    return object
      .keys(styles)
      .filter(function (key) {
        return styles[key] != null;
      })
      .map(function (key) {
        return [key, [styles[key]]].join(':');
      })
      .join(';');
  }

  return styles;
}

module.exports = style;
