<template>
  <div
    v-if="show"
    class="h-96rpx w-full flex items-center bg-contain bg-no-repeat"
    :style="{ backgroundImage: `url(${PROXY_IMAGES.couponBgd})` }"
  >
    <div
      class="w-150rpx flex items-end justify-center px-20rpx color-[#FF0000]"
    >
      <div class="text-[48rpx] fw-600 leading-[40rpx]">
        {{ sliceNumberDecimal(amount > 10000 ? amount / 10000 : amount, 1) }}
      </div>
      <div class="text-[22rpx]">{{ amount > 10000 ? "万元" : "元" }}</div>
    </div>
    <div class="flex flex-col flex-1 color-white">
      <div class="text-[26rpx]">您有{{ waitActiveCount }}张优惠劵待激活</div>
      <div class="text-[22rpx]">激活领取后, 即可使用</div>
    </div>
    <div
      class="mr-20rpx h-56rpx w-140rpx rounded-[8rpx] text-center text-[26rpx] color-[#FF0000] leading-[56rpx]"
      style="background: linear-gradient(90deg, #f9fdef 0%, #ffedd1 100%)"
      @click="
        () => openWebview({ url: '/coupon-h5/pages/index?type=WAIT_ACTIVE' })
      "
    >
      点击激活
    </div>
  </div>
</template>

<script setup lang="ts">
import { PROXY_IMAGES } from "@/composables/helpers/preload";
import { sliceNumberDecimal } from "@/utils/format";

withDefaults(
  defineProps<{
    show?: boolean;
    amount?: number;
    // 待激活数量
    waitActiveCount?: number;
  }>(),
  {
    amount: 0,
    waitActiveCount: 0,
  },
);

const { openWebview } = useNavigate({
  webview: { withToken: true, autoOrigin: true },
});
</script>

<style scoped></style>
