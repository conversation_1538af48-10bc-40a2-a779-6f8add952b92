{
  "explorer.fileNesting.patterns": {
    "vite.config.*": "pages.config.*, manifest.config.*, unocss.config.*, volar.config.*, *.env, .env.*"
  },
  "editor.defaultFormatter": "dbaeumer.vscode-eslint",
  "references.preferredLocation": "peek",
  "editor.formatOnSave": true,
  "prettier.enable": true,
  // Auto fix
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.fixAll.stylelint": "explicit",
    "source.organizeImports": "never"
  },
  "vue.codeActions.enabled": false,
  "i18n-ally.localesPaths": ["locales"],
  "i18n-ally.sourceLanguage": "zh_CN",
  "i18n-ally.displayLanguage": "zh_CN",
  "i18n-ally.keystyle": "flat",
  "eslint.lintTask.enable": true,
  "scss.lint.unknownAtRules": "ignore",
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  }
}
