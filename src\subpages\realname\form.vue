<template>
  <div class="bg-#f4f5f7 px-24rpx pt-30rpx">
    <div class="mb-30rpx rounded-12rpx bg-#fff">
      <nut-form ref="formRef" :rules="rules" :model-value="formData">
        <FormItem
          v-model="formData.realName"
          class="border"
          :item="formConfig.realName"
        />
        <FormItem :item="formConfig.credentialType">
          <nut-row type="flex" align="center" gutter="10">
            <nut-col :span="21">
              <div @click="togglePicker(true)">
                {{
                  credentialTypeName || formConfig.credentialType.placeholder
                }}
              </div>
            </nut-col>
            <nut-col :span="3">
              <nut-icon name="arrow-right" size="14px" color="#666666" />
            </nut-col>
          </nut-row>
        </FormItem>

        <FormItem
          v-model="formData.credentialId"
          :item="formConfig.credentialId"
        />
      </nut-form>
    </div>
    <FormSubmitBtn
      :gutter-x="false"
      :gutter-top="false"
      :loading="loading"
      @click="submit"
    >
      开始认证
    </FormSubmitBtn>
  </div>

  <nut-popup
    v-model:visible="picker"
    round
    position="bottom"
    safe-area-inset-bottom
    :custom-style="{ height: '40%' }"
  >
    <nut-picker
      :columns="pickerOptions"
      title="证件类型"
      @cancel="togglePicker(false)"
      @confirm="pickerConfirm"
    />
  </nut-popup>
</template>

<script setup lang="ts">
import type { OcrTypes, UserTypes } from "@/api";
import type { FormInst, PickerBaseEvent, PickerOption } from "nutui-uniapp";
import { AUTH_CARD_TYPE_MAP } from "@/enums";
import { useToggle } from "@vueuse/core";

useTitle(t("shi-ming-ren-zheng"));
const [loading, toggleLoading] = useToggle(false);
const [picker, togglePicker] = useToggle(false);
const { openWebview } = useNavigate();
const formRef = ref<FormInst>();
const pickerOptions = ref<PickerOption[]>(
  ["CH_ID_CARD", "CH_HK_MACAO_CARD"].map(key => {
    return {
      value: key,
      text: AUTH_CARD_TYPE_MAP[key],
    };
  }),
);

const { formData, formConfig, rules } = useForm<UserTypes.IPersonalVerifyDto>({
  address: {},
  realName: {
    label: t("zhen-shi-xing-ming"),
    maxLength: 10,
    rules: [FormRules.required()],
  },
  credentialType: {
    value: "CH_ID_CARD",
    label: t("zheng-jian-lei-xing"),
    rules: [FormRules.required()],
  },
  credentialId: {
    label: t("zheng-jian-hao-ma"),
    rules: [FormRules.required()],
  },
  idcardFrontUrl: {},
  isFromPartner: {
    value: false,
  },
});

const { getCamera } = useWxPermission();

const credentialTypeName = computed(
  () => AUTH_CARD_TYPE_MAP[formData.credentialType] ?? "",
);

function pickerConfirm({ selectedOptions }: PickerBaseEvent) {
  const item = selectedOptions?.[0];
  formData.credentialType = item.value as typeof formData.credentialType;
  togglePicker(false);
}

function ocrCallback(param: {
  data: OcrTypes.ICommonBusinessLicenseResponseVo;
  url: string;
}) {
  formData.address = param?.data?.address ?? "";
  formData.credentialId = param?.data?.credentialId ?? "";
  formData.realName = param?.data?.realName ?? "";
  formData.idcardFrontUrl = param?.url ?? "";
}

function submit() {
  formRef.value?.validate().then(async ({ valid }) => {
    if (valid) {
      toggleLoading(true);
      try {
        const { data } = await userApi.ApiAuthNewpersonauthPost(formData, {
          showError: true,
        });
        if (!data?.redirect) return false;
        useUserStore().queryUserInfo();
        openWebview({
          url: data.redirect,
        });
      } finally {
        toggleLoading(false);
      }
    }
  });
}
</script>

<style lang="scss" scoped>
::v-deep .nut-cell-group__wrap {
  @apply my-0;
}
</style>
