<template>
  <div />
</template>

<script setup lang="ts">
import { Config } from "@/config";
import { MerchantCategoryEnum } from "@/enums";

interface SceneData {
  incodes?: string;
  inviter?: string;
  serviceCode?: string;
}

const parseData = ref<SceneData>({});
const userStore = useUserStore();
const { openWebview, withLogin } = useNavigate({
  webview: { withToken: true },
});

async function isDomesticIndividualBiz(serviceCode?: string) {
  if (!serviceCode) return false;
  const rsp = await merchantApi.ApiAppsellerGetallservicedetailPost({
    serviceCodes: [serviceCode],
  });

  const service = rsp?.data?.services?.[0];

  return (
    service?.openMerchantCategory === MerchantCategoryEnum.DomesticIndividual
  );
}

// {"scene": "i%3DWSATGS%26u%3D20033841%26s%3DP03"}
onLoad(query => {
  withLogin({ returnTo: true }).run(async () => {
    parseData.value = parseSceneData(query);
    const isDomesticIndividual = await isDomesticIndividualBiz(
      parseData.value.serviceCode,
    );

    await Promise.all([
      userStore.queryMerchantList(),
      userStore.queryUserInfo(),
    ]);

    const path = userStore.activeMerchant?.partnerCode
      ? "activate-service"
      : "registration";
    let url = `${Config.merchantUrl}/${path.toParamsUrl(parseData.value)}`;

    url =
      isDomesticIndividual &&
      userStore.userInfo?.realnameAuthStatus !== "SUCCESS"
        ? `${Config.merchantUrl}/auth/realname`.toParamsUrl({ redirect: url })
        : url;

    openWebview({
      url,
      isRedirect: true,
    });
  });
});

function parseSceneData(
  sceneObj?: Record<string, string>,
): Record<string, string> {
  try {
    if (sceneObj?.scene) {
      const decodeScene = decodeURIComponent(sceneObj.scene);
      const params = parseUrlQuery(decodeScene);
      const res = {
        incodes: params.i,
        inviter: params.u,
        serviceCode: params.s,
      };
      return res;
    }
  } catch (err) {
    console.error("Error parsing scene:", err);
    return {};
  }

  return {};
}

function parseUrlQuery(queryString: string) {
  const params: Record<string, string> = {};
  if (!queryString) return params;

  queryString.split("&").forEach(pair => {
    const [key, value] = pair.split("=");
    if (key) {
      params[decodeURIComponent(key)] = decodeURIComponent(value || "");
    }
  });

  return params;
}
</script>
