<template>
  <nut-form ref="ruleForm" :rules="rules" :model-value="formData">
    <FormItem v-model="formData.content" :item="formConfig.content" />
  </nut-form>
  <FormSubmitBtn
    :loading="loading"
    :disabled="!formData.content"
    @click="submit"
  >
    {{ $t("ti-jiao-fan-kui") }}
  </FormSubmitBtn>
</template>

<script setup lang="ts">
import type { FormInst } from "nutui-uniapp";
import { delay } from "oig-utils";

useTitle(t("yi-jian-fan-kui"));

const ruleForm = ref<FormInst | null>(null);
const loading = ref(false);

const { formData, formConfig, rules } = useForm({
  content: {
    type: "textarea",
    rules: [FormRules.required()],
    placeholder: t("feedbackplaceholder"),
    maxLength: 300,
  },
});

function submit() {
  ruleForm.value?.validate().then(async ({ valid }) => {
    if (valid) {
      try {
        loading.value = true;
        await platformApi.ApiFeedbackAddPost(formData, { showError: true });
        uni.navigateBack();
        await delay(300);
        showMsg("反馈成功，感谢您对优顶特的支持");
      } finally {
        loading.value = false;
      }
    }
  });
}
</script>
