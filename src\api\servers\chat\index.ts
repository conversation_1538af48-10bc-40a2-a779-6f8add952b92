import type { StreamResponse } from "@/api/adapter";
import type { AxiosInstance, CancelToken } from "axios";
import type * as ChatTypes from "./type";
import { adapter, streamAdapter } from "@/api/adapter";
import { chatTimeout, timeout } from "@/api/constant";
import { requestInterceptor } from "@/api/interceptors/request";
import { responseAdapter } from "@/api/interceptors/response";
import axios from "axios";
import { customErrorAdapter } from "./customErrorAdapter";

class ChatApi {
  request: AxiosInstance;
  streamRequest: AxiosInstance;

  constructor(private basePath: string) {
    this.request = axios.create({
      baseURL: this.basePath,
      adapter,
      timeout,
    });

    this.streamRequest = axios.create({
      baseURL: this.basePath,
      adapter: streamAdapter,
      timeout: chatTimeout,
    });

    this.request.interceptors.request.use(requestInterceptor);
    this.request.interceptors.response.use(responseAdapter, customErrorAdapter);

    this.streamRequest.interceptors.request.use(requestInterceptor);
    this.streamRequest.interceptors.response.use(
      responseAdapter,
      customErrorAdapter,
    );
  }

  public async fetchSessionId(): Promise<string> {
    return this.request.post("/chat/message", {}).then(res => res.data);
  }

  public async fetchMessage(
    params: ChatTypes.MessageReq,
    transformResponse: (params: StreamResponse) => void,
    cancelToken?: CancelToken,
  ) {
    return this.streamRequest.post(
      "/demo/run_agent_ydt",
      {
        ...params,
      },
      {
        transformResponse,
        cancelToken,
        headers: {
          Accept: "text/event-stream",
          "Content-Type": "application/json;charset=UTF-8",
        },
      },
    );
  }

  public async fetchCancelAgent(
    params: ChatTypes.CancelAgentReq,
  ): Promise<string> {
    return this.request
      .get("/demo/cancel_agent", {
        params: { ...params },
      })
      .then(res => res.data.data);
  }

  public async fetchUserSession(
    params: ChatTypes.SessionReq,
  ): Promise<ChatTypes.UserSession> {
    return this.request
      .get("/demo/crud/get_user_session", {
        params: { ...params },
      })
      .then(res => res.data.data);
  }

  public async fetchGetSessionInfo(
    params: ChatTypes.GetSessionInfoReq,
  ): Promise<ChatTypes.GetSessionInfoRes> {
    return this.request
      .get("/demo/crud/get_session_info", {
        params: { ...params },
        showError: true,
      })
      .then(res => res.data.data);
  }

  public async fetchFeedback(params: ChatTypes.FeedbackReq): Promise<boolean> {
    return this.request
      .post("/demo/crud/user/comment", params)
      .then(res => res.data);
  }

  public async fetchGetXyTips(
    params: ChatTypes.GetXyTipsRes,
  ): Promise<ChatTypes.GetXyTipsReq> {
    return this.request
      .post("/demo/get_xy_tips", {
        ...params,
      })
      .then(res => res.data.data);
  }

  public async fetchSystemUsage(): Promise<ChatTypes.SystemUsageType> {
    return this.request.get("/announcement/list").then(res => res.data);
  }

  public async fetchSystemDefaultData(): Promise<ChatTypes.SystemDefaultDataType> {
    return this.request.get("/announcement/info").then(res => res.data);
  }
}

export const chatApi = new ChatApi(import.meta.env.VITE_CHAT_GATWAY);
