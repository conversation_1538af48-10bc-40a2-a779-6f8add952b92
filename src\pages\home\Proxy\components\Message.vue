<template>
  <div class="rounded-card p-[16rpx]">
    <div class="mb-[28rpx] flex items-center justify-between">
      <div class="text-[30rpx] text-black font-600">
        {{ $t("zui-xin-xiao-xi") }}
      </div>
      <uni-icons type="right" width="30rpx" height="30rpx" @click="toMessage" />
    </div>

    <div v-if="messageList.length" @click="toMessage">
      <div
        v-for="item in messageList"
        :key="item.id"
        class="mb-[16rpx] flex items-center"
      >
        <div
          class="mr-10rpx border-1rpx border-primary rounded-2rpx border-solid bg-[#E4F6FF] px8rpx text-[18rpx] color-primary lh-30rpx"
        >
          {{ item.dictDetailName }}
        </div>
        <div class="flex-1 text-ellipsis text-[26rpx] text-black lh-36rpx">
          {{ item.content }}
        </div>
      </div>
    </div>
    <div v-else class="mb-24rpx text-[22rpx] color-#C7CACC">
      {{ $t("zan-wu-shu-ju") }}
    </div>

    <swiper
      v-if="props.order.length || reservation.length || progress.length"
      autoplay
      disable-touch
      circular
      indicator-dots
      :touchable="false"
      indicator-active-color="#0091DB"
      indicator-color="#D8D8D8"
      class="h-200rpx"
    >
      <swiper-item v-for="item in props.order" :key="item.orderId">
        <div
          class="h-200rpx rounded-12rpx bg-[#F0FAFF] px-20rpx py-20rpx"
          @click="skip($t('liu-xiang-an-pai'), '/proxy-h5/flow/index')"
        >
          <div class="mb-14rpx flex items-center justify-between">
            <div class="text-[30rpx] color-black font-600">
              {{ item?.cabinetNo }}
            </div>
            <div class="flex items-center justify-center">
              <div class="text-[26rpx] color-#0091DB">
                {{ $t("qian-wang-an-pai-liu-xiang") }}
              </div>
              <uni-icons
                color="#0091DB"
                type="right"
                width="30rpx"
                height="30rpx"
              />
            </div>
          </div>
          <div
            class="flex items-center justify-between text-[26rpx] color-grey"
          >
            <div>{{ `ETA:${item?.finalVoyageEta || ""}` }}</div>
            <div>{{ `${$t("mu-de-gang")}:${item?.portName}` }}</div>
          </div>
          <div
            class="line-clamp-2 mt-10rpx text-ellipsis text-[26rpx] color-grey"
          >
            {{ orderGoods(item) }}
          </div>
        </div>
      </swiper-item>

      <swiper-item v-for="item in reservation" :key="item.id">
        <div
          class="h-200rpx rounded-12rpx bg-[#F0FAFF] px-20rpx py-20rpx"
          @click="skip($t('chu-ku-guan-li'), '/proxy-h5/stock/stockOut')"
        >
          <div class="mb-14rpx flex items-center justify-between">
            <div class="text-[30rpx] color-black font-600">
              {{ item?.cabinetNos }}
            </div>
            <div class="flex items-center justify-center">
              <div class="text-[26rpx] color-#0091DB">
                {{ $t("dai-bu-chong-che-liang-xin-xi") }}
              </div>
              <uni-icons
                color="#0091DB"
                type="right"
                width="30rpx"
                height="30rpx"
              />
            </div>
          </div>
          <div
            class="flex items-center justify-between text-[26rpx] color-grey"
          >
            <div>{{ `${$t("leng-ku")}:${item?.warehouseName}` }}</div>
            <div>
              {{ `${$t("chu-ku-ri-qi")}:${item?.planExportDate}` }}
            </div>
          </div>
          <div
            class="line-clamp-2 mt-10rpx text-ellipsis text-[26rpx] color-grey"
          >
            {{ reservationGoods(item) }}
          </div>
        </div>
      </swiper-item>

      <swiper-item v-for="(item, index) in progress" :key="index">
        <div
          class="h-200rpx rounded-12rpx bg-[#F0FAFF] px-20rpx py-20rpx"
          @click="skip($t('chu-ku-guan-li'), '/proxy-h5/stock/stockOut')"
        >
          <div class="mb-14rpx flex items-center justify-between">
            <div class="text-[30rpx] color-black font-600">
              {{ item?.cabinetNo }}
            </div>
            <div class="flex items-center justify-center">
              <div class="text-[26rpx] color-#0091DB">
                {{ $t("dai-bu-chong-che-liang-xin-xi") }}
              </div>
              <uni-icons
                color="#0091DB"
                type="right"
                width="30rpx"
                height="30rpx"
              />
            </div>
          </div>
          <div
            class="flex items-center justify-between text-[26rpx] color-grey"
          >
            <div>{{ `${$t("leng-ku")}:${item?.warehouseName}` }}</div>
            <div>
              {{ `${$t("chu-ku-ri-qi")}:${item?.stockOutPlanDate}` }}
            </div>
          </div>
          <div
            class="line-clamp-2 mt-10rpx text-ellipsis text-[26rpx] color-grey"
          >
            {{ progressGoods(item) }}
          </div>
        </div>
      </swiper-item>
    </swiper>
  </div>
</template>

<script lang="ts" setup>
import type { AgentCustomerTypes, PlatformTypes } from "@/api";

const props = defineProps<{ order: AgentCustomerTypes.IOrderVo[] }>();

const { openWebview } = useNavigate({
  webview: { withToken: true, autoOrigin: true },
});

const userStore = useUserStore();
const { userInfo } = storeToRefs(userStore);

onBeforeMount(() => {
  getData();
});

function getData() {
  getMessage();
  getReservation();
  getProgress();
}

const messageList = ref<PlatformTypes.IMessageVo[]>([]);
async function getMessage() {
  const res = await platformApi.ApiMessageAppmessagelistPageLimitPost(
    { userId: userInfo.value?.userId, appType: "UCHAIN_ROLE" },
    { limit: 3, page: 1 },
  );
  messageList.value = res.data.list;
}

// 待流向安排商品展示
function orderGoods(item: AgentCustomerTypes.IOrderVo) {
  const goods: {
    key: string;
    name: string;
    value: AgentCustomerTypes.IOrderDetailVo[];
  }[] = [];

  item?.orderDetailList?.forEach(i => {
    const key = `${i.countryName}|${i.factoryCode}`;
    const index = goods.findIndex(j => j.key === key);
    if (index === -1) {
      goods.push({
        key,
        name: i.spName || "",
        value: [i],
      });
    } else {
      goods[index].value.push(i);
    }
  });
  return goods.map(i => `【${i.key}】${i.name}`).join("；");
}

// // 出库-预约中
const reservation = ref<AgentCustomerTypes.IStockOutPlanVo[]>([]);
async function getReservation() {
  const res = await agentCustomerApi.ApiStockoutStockoutplanlistPost({
    pageNo: 1,
    pageSize: 3,
    status: "01",
  });
  reservation.value = res.data?.list || [];
}
// 出库-预约中商品展示
function reservationGoods(item: AgentCustomerTypes.IStockOutPlanVo) {
  const goods: {
    key: string;
    value: string;
  }[] = [];

  item?.detailList?.forEach(i => {
    const key = `${i.countryName}|${i.factoryCode}`;
    const index = goods.findIndex(j => j.key === key);
    const value = `${i.goodsName}*${i.pieceNum};`;
    if (index === -1) {
      goods.push({ key, value });
    } else {
      goods[index].value += value;
    }
  });
  return goods.map(i => `【${i.key}】${i.value}`).join("；");
}

// 出库-办理中
const progress = ref<AgentCustomerTypes.IStockOutVo[]>([]);
async function getProgress() {
  const res = await agentCustomerApi.ApiStockStockoutpagePost({
    pageNo: 1,
    pageSize: 3,
    sapStockStatus: "A",
  });

  // @ts-ignore 后端接口定义错误
  progress.value = res.data?.list || [];
}
// 出库-办理中商品展示
function progressGoods(item: AgentCustomerTypes.IStockOutVo) {
  const goods: {
    key: string;
    value: string;
  }[] = [];

  item?.detailList?.forEach(i => {
    const key = `${i.countryName}|${i.factoryCode}`;
    const index = goods.findIndex(j => j.key === key);
    const value = `${i.goodsName}*${i.realPiece};`;
    if (index === -1) {
      goods.push({ key, value });
    } else {
      goods[index].value += value;
    }
  });
  return goods.map(i => `【${i.key}】${i.value}`).join("；");
}

function skip(title: string, url: string) {
  openWebview({ title, url });
}

function toMessage() {}

defineExpose({
  getData,
});
</script>
