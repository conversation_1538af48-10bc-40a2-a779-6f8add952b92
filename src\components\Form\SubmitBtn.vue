<template>
  <div :class="{ 'mt-60rpx': gutterTop, 'mx-30rpx': gutterX }">
    <nut-button
      block
      :loading="loading"
      :disabled="disabled"
      size="large"
      type="primary"
      shape="square"
      @click="emit('click')"
    >
      <slot />
    </nut-button>
  </div>
</template>

<script setup lang="ts">
withDefaults(
  defineProps<{
    loading?: boolean;
    disabled?: boolean;
    gutterTop?: boolean;
    gutterX?: boolean;
  }>(),
  {
    loading: false,
    disabled: false,
    gutterTop: true,
    gutterX: true,
  },
);
const emit = defineEmits(["click"]);
</script>
