page {
  --nut-primary-color: theme("colors.primary");
  --nut-button-primary-background-color: linear-gradient(
    315deg,
    theme("colors.primary") 0%,
    theme("colors.primary") 100%
  );
  --nut-button-primary-border-color: theme("colors.primary");
  --nut-checkbox-label-margin-left: 8rpx;
  --nut-checkbox-label-color: theme("colors.grey");
  --nut-checkbox-label-font-size: 24rpx;
  --nut-checkbox-icon-font-size: 32rpx;
  --nut-tabs-horizontal-tab-line-color: theme("colors.primary");
  --nut-tabs-titles-item-active-color: theme("colors.primary");
  --nut-tabs-titles-background-color: theme("colors.white");
  --nut-tabs-titles-item-color: theme("colors.grey");
  --nut-cell-box-shadow: none;
  --nut-cell-after-border-bottom: 1px solid theme("colors.grey.light");
  --nut-button-large-font-size: 34rpx;
  --nut-cell-desc-color: theme("colors.black");
  --nut-cell-padding: 18px 16px;
  --un-ring-offset-shadow: 0 0 0 0 transparent;
  --un-ring-shadow: 0 0 0 0 transparent;
  --nut-toast-inner-padding: 15px;
  --nut-grid-item-content-padding: 2px 8px;
}

.nut-button--square {
  border-radius: 4px !important;
}

.nut-form-item {
  align-items: center;
  box-shadow: none !important;
}

.nut-cell__link.nut-icon-right {
  margin-left: 10rpx;
  font-size: 22rpx !important;
}

.nut-dialog {
  padding-top: 20px !important;
}
