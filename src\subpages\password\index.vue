<template>
  <nut-form ref="ruleForm" :rules="rules" :model-value="formData">
    <FormItem v-model="formData.phone" :item="formConfig.phone" />
    <FormItem :item="formConfig.code">
      <nut-row type="flex" align="center" gutter="10">
        <nut-col :span="14">
          <nut-input
            v-model="formData.code"
            :placeholder="formConfig.code.placeholder"
            max-length="6"
            type="number"
            clearable
          />
        </nut-col>
        <nut-col :span="10">
          <SmsBtn
            :payload="{
              phoneNumber: formData.phone,
              codeUseType: 'RESET_PASSWORD',
            }"
          />
        </nut-col>
      </nut-row>
    </FormItem>
    <FormItem v-model="formData.password" :item="formConfig.password" />
    <FormItem v-model="formData.newPassword" :item="formConfig.newPassword" />
  </nut-form>
  <FormSubmitBtn :loading="loading" @click="submit">
    {{ $t("que-ren") }}
  </FormSubmitBtn>
</template>

<script setup lang="ts">
import type { FormInst } from "nutui-uniapp";

const props = defineProps<{
  submit: (payload: typeof formData) => Promise<any>;
}>();
const { isLogined, userInfo } = storeToRefs(useUserStore());

const ruleForm = ref<FormInst | null>(null);
const loading = ref(false);

const { formData, formConfig, rules } = useForm({
  phone: {
    label: t("shou-ji-hao"),
    type: "number",
    disabled: isLogined.value,
    value: isLogined.value ? userInfo.value?.mobileNum : "",
    rules: [FormRules.required(), FormRules.isPhone()],
  },
  code: {
    label: t("duan-xin-yan-zheng-ma"),
    rules: [FormRules.required()],
  },
  password: {
    label: t("mi-ma"),
    type: "password",
    rules: [FormRules.required(), FormRules.passwordLength()],
  },
  newPassword: {
    label: t("que-ren-mi-ma"),
    type: "password",
    rules: [
      FormRules.required(),
      FormRules.passwordLength(),
      {
        validator: (value): boolean => value === formData.password,
        message: "两次密码输入不一致",
      },
    ],
  },
});

function submit() {
  ruleForm.value?.validate().then(async ({ valid }) => {
    if (valid) {
      loading.value = true;
      try {
        await props.submit(formData);
      } finally {
        loading.value = false;
      }
    }
  });
}
</script>
