<template>
  <div class="h-100vh overflow-hidden bg-white">
    <div
      class="mt-34rpx overflow-scroll px-24rpx"
      style="height: calc(100vh - 88rpx - env(safe-area-inset-bottom))"
    >
      <div class="mb-34rpx text-40rpx text-#33373f font-600">
        {{ detailData?.contentTitle }}
      </div>
      <nut-row type="flex" align="center">
        <nut-col :span="6">
          <div class="flex items-center">
            <image src="@/static/eye-icon.png" class="size-36rpx" />
            <div class="ml-8rpx text-26rpx text-#878c94">
              {{ detailData?.allContentHit }}阅读
            </div>
          </div>
        </nut-col>
        <nut-col :span="15">
          <div class="flex items-center">
            <image src="@/static/time-icon.png" class="size-36rpx" />
            <div class="ml-8rpx text-26rpx text-#878c94">
              发布于：{{ detailData?.contentDateStr }}
            </div>
          </div>
        </nut-col>
        <nut-col :span="3">
          <image
            v-if="detailData?.vipFlag"
            h-32rpx
            w-70rpx
            src="@/static/vip.png"
            alt=""
          />
        </nut-col>
      </nut-row>
      <div v-if="detailData?.isDetail === 0">
        <div class="mt-40rpx">
          <image
            h-862rpx
            w-702rpx
            :src="detailData?.guideImage"
            mode="scaleToFill"
          />
        </div>
        <div class="fixed bottom-40rpx left-0 z-10 h-140rpx w-full flex">
          <image class="absolute z-1 h-140rpx w-full" :src="vipBtn" />
          <div
            class="absolute right-76rpx top-50rpx z-2 rd-34rpx bg-#E5B891 px-30rpx py-10rpx text-26rpx text-#394253 font-600"
            @click="unlockRights"
          >
            点击解锁权益
          </div>
        </div>
      </div>
      <div v-else>
        <div
          v-if="detailData?.pdfFile"
          class="mb-22rpx mt-32rpx f-c justify-between bg-#fff3f3 py-24rpx pl-8rpx pr-30rpx"
          @click="toPdf"
        >
          <div class="f-c">
            <image src="@/static/pdf-icon.png" class="size-72rpx" />
            <div class="text-30rpx text-#33373f font-600">
              {{ detailData.pdfFileName }}
            </div>
          </div>
          <image src="@/static/right-arrow.png" class="size-48rpx" />
        </div>
        <MpHtml
          :content="formatContent(detailData?.content)"
          @imgtap="onImgTap"
        />
        <div class="mt-28rpx text-28rpx text-#878c94">
          <span>内容来源于：{{ detailData?.contentSource }}</span>
        </div>
      </div>
    </div>
    <nut-watermark
      v-if="showWatermark"
      :content="userInfo?.userId?.toString()"
    />
  </div>
</template>

<script setup lang="ts">
import type { IArticleVo } from "news-api/src/queryBackend/types";
import MpHtml from "@/subpages/components/MpHtml2/components/mp-html/mp-html.vue";

const props = defineProps<{
  detailData: IArticleVo;
}>();

const { userInfo, isLogined } = storeToRefs(useUserStore());
const vipBtn = "unlock-vip.png".toQiniuUrl();
const { withLogin } = useNavigate();

const showWatermark = computed(() => {
  return (
    props.detailData.isDetail !== 0 &&
    isLogined.value &&
    props.detailData.vipFlag
  );
});

function unlockRights() {
  if (isLogined.value) {
    uni.navigateTo({
      url: "/subpages/vip-service/index",
    });
  } else {
    withLogin({ returnTo: true }).run(() => {});
  }
}

function onImgTap(e: any) {
  const url = e.detail.src;
  if (url) {
    uni.previewImage({
      urls: [url],
    });
  }
}
function formatContent(content?: string) {
  if (!content) return "";
  return (
    content
      .replace(
        /<p([\s\w"=/.:;]+)((style="[^"]+"))/gi,
        "<p style='font-size: 15px; line-height: 32px;max-width:100%'",
      )
      .replace(
        /<section>/gi,
        "<section style='font-size: 15px; line-height: 32px;max-width:100%'>",
      )
      .replace(
        /<span>/gi,
        "<span style='font-size: 15px; line-height: 32px;max-width:100%'>",
      )
      /* eslint-disable regexp/no-obscure-range */
      .replace(/<video([\s\w"-=]+)((width="[^"]+"))/gi, "<video$1 width='100%'")
      .replace(/<img([\s\w"-=]+)((height="[^"]+"))/gi, "<img$1")
      .replace(/<img([\s\w"-=]+)((width="[^"]+"))/gi, "<img$1")
      .replace(/<img([\s\w"-=]+)((style="[^"]+"))/gi, "<img$1")
      .replace(/<img([\s\w"-=]+)((alt="[^"]+"))/gi, "<img$1")
      .replace(/<img([\s\w"-=]+)(\/)/gi, "<img$1")
      .replace(/<img([\s\w"-=]+)/gi, "<img$1 style=\"max-width: 100%\" /")
      .replace(/\.webp/g, ".jpg")
      .replace(/https?/g, "https")
  );
}

function toPdf() {
  const { pdfFile, pdfFileName, vipFlag } = props.detailData;
  if (pdfFile) {
    uni.navigateTo({
      url: `/pages/webview/previewDocument?url=${pdfFile}&fileName=${pdfFileName}&showMenu=${
        vipFlag ? "false" : "true"
      }`,
    });
  }
}
</script>

<style scoped lang="scss">
::v-deep .nut-row {
  @apply mb-22rpx;
}
</style>
