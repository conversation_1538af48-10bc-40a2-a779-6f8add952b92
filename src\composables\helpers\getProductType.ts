import type { MatchTypes } from "@/api";

type AnyProduct =
  | MatchTypes.ProductAppRsp
  | MatchTypes.PurchaseProductAppRsp
  | MatchTypes.SupplyProductAppRsp;

export function isSupplyType(
  data: AnyProduct,
): data is MatchTypes.SupplyProductAppRsp {
  return "supplyImage" in data;
}

export function isPurchaseType(
  data: AnyProduct,
): data is MatchTypes.PurchaseProductAppRsp {
  return !isSupplyType(data);
}
