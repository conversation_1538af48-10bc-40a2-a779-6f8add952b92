export function useElementRect() {
  const instance = getCurrentInstance();
  const query = uni.createSelectorQuery().in(instance);
  const getRect = async (
    selector: string,
  ): Promise<UniApp.NodeInfo | UniApp.NodeInfo[]> => {
    return new Promise((resolve, reject) => {
      try {
        query
          .select(selector)
          .fields({ size: true, scrollOffset: true }, data => {
            if (data) {
              resolve(data);
            } else {
              reject(new Error(`未找到元素: ${selector}`));
            }
          })
          .exec();
      } catch (error) {
        reject(error);
      }
    });
  };

  return { query, getRect };
}
