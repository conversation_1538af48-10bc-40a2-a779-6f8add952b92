export interface Options {
  arrayPrefix?: string;
}

export type Param = string | number | boolean | object | Array<Param>;
export type Params = Array<Param> | Record<string, Param>;

const euc = encodeURIComponent;

function keyValueToQueryString(
  key: string,
  value: Record<string, Param> | Param,
  queryString: string,
  isArray: boolean,
  options: Options,
): string {
  const arrayPrefix = isArray ? options.arrayPrefix || "" : "";

  if (typeof value === "object") {
    const tmpQueryString = `${key}${arrayPrefix}${queryString && "]"}[`;

    return `${objectToQueryString(
      value as Record<string, Param>,
      `${queryString}${tmpQueryString}`,
      options,
    )}`;
  }

  if (queryString && queryString.length) {
    return `${queryString}${key}]${arrayPrefix}=${euc(value)}`;
  }

  return `${key}${arrayPrefix}=${euc(value)}`;
}

function arrayToQueryString(
  key: string,
  values: Array<Params | Param>,
  queryString: string,
  options: Options,
): string {
  return values
    .map(value => keyValueToQueryString(key, value, queryString, true, options))
    .join("&");
}

export function objectToQueryString(
  params: Params,
  queryString: string = "",
  options: Options = {},
): string {
  let paramsStringArray = [];

  if (Array.isArray(params)) {
    paramsStringArray = params.map((value, index) =>
      keyValueToQueryString(`${index}`, value, queryString, true, options),
    );
  } else {
    paramsStringArray = Object.keys(params)
      .filter(key => params[key] !== undefined) // Can be 0
      .map(key =>
        params[key] && Array.isArray(params[key])
          ? arrayToQueryString(
              `${key}`,
              params[key] as Array<Params | Param>,
              queryString,
              options,
            )
          : keyValueToQueryString(
              key,
              params[key],
              queryString,
              false,
              options,
            ),
      );
  }
  return paramsStringArray.join("&").replace(/%20/g, "+");
}

// 解析 path
export function parseUrl(fullPath: string) {
  const [path, queryStr] = fullPath.split("?");
  const name = path.slice(path.lastIndexOf("/") + 1);
  const query: { [key: string]: unknown } = {};
  // 匹配路径
  const regex = /^(?:https?:\/\/)?[^/]+(\/[^?#]*)?(\?[^#]*)?$/;
  const match = fullPath.match(regex);
  const childPath = match?.[1] || "";

  queryStr
    ?.split("&")
    .map(i => i.split("="))
    .forEach(i => (query[i[0]] = i[1]));
  return {
    name,
    childPath,
    path,
    query,
  };
}

export function decodeURIObject(obj?: AnyObject) {
  if (!obj) {
    return {};
  }
  const result: AnyObject = {};
  for (const key in obj) {
    result[key] = decodeURIComponent(obj[key]);
  }

  return result;
}

export function parseFileFromUrl(url: string) {
  const { name } = parseUrl(url);
  const match = name.match(/\.([^.]+)$/);

  return {
    name,
    fileType: match ? match[1] : "",
  };
}

export function removeQueryParam(url: string, param: string): string {
  const regex = new RegExp(`([?&])${param}=[^&]*(&|$)`);

  const updatedUrl = url.replace(regex, "$1").replace(/([&?])$/, "");

  return updatedUrl.includes("?")
    ? updatedUrl
    : updatedUrl.replace(/(\?.*)$/, "");
}

export function addQueryParam(
  url: string,
  param: string,
  value: string | number,
): string {
  const separator = url.includes("?") ? "&" : "?";
  const newParam = `${param}=${encodeURIComponent(value)}`;

  // 如果参数已经存在，先移除旧的参数
  const updatedUrl = removeQueryParam(url, param);

  return updatedUrl + separator + newParam;
}
