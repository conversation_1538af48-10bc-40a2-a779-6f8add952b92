<template>
  <scroll-view
    :style="{ height: scrollViewHeight }"
    scroll-y
    refresher-enabled
    :refresher-triggered="refreshing"
    :refresher-threshold="500"
    :lower-threshold="1000"
    @refresherrefresh="onRefresh"
  >
    <template v-if="show">
      <div class="mb-20rpx flex items-center justify-between">
        <div class="flex items-center text-26rpx color-black">
          <image
            src="@/static/enterprise.png"
            class="mr-8rpx h-36rpx w-36rpx"
          />
          <div>{{ activeMerchant?.realName }}</div>
        </div>
        <div
          class="h-36rpx rounded-8rpx bg-[#FEEDED] px-8rpx text-22rpx color-[#FF0000] lh-36rpx solid-1rpx-#FF0000"
          @click="skipCoupon()"
        >
          {{ displayText }}
        </div>
      </div>
      <Total ref="TotalRef" />
    </template>
    <GridMenu ref="GridMenuRef" />
    <template v-if="show">
      <CouponCountCard
        ref="CouponCountRef"
        :show-tip="showCouponCountTip"
        :wait-active-count="coupon.waitActiveCount"
      />

      <Message ref="MessageRef" :order="order.orderVos || []" />
      <ExchRate ref="ExchRateRef" />
      <Tool ref="ToolRef" />
    </template>
    <Null v-else ref="NullRef" />
  </scroll-view>
</template>

<script setup lang="ts">
import type { AgentCustomerTypes, CouponTypes } from "@/api";
import { useHomeView } from "../useHomeView";
import CouponCountCard from "./components/CouponCountCard/index.vue";
import ExchRate from "./components/ExchRate.vue";
import GridMenu from "./components/GridMenu.vue";
import Message from "./components/Message.vue";
import Null from "./components/Null.vue";
import Tool from "./components/Tool.vue";
import Total from "./components/Total.vue";

const { scrollViewHeight, refreshing, refreshHomeView } = useHomeView();

const userStore = useUserStore();
const { activeMerchant, isLogined } = storeToRefs(userStore);

const { openWebview } = useNavigate({
  webview: { withToken: true, autoOrigin: true },
});

const TotalRef = ref<InstanceType<typeof Total> | null>(null);
const GridMenuRef = ref<InstanceType<typeof GridMenu> | null>(null);
const MessageRef = ref<InstanceType<typeof Message> | null>(null);
const ExchRateRef = ref<InstanceType<typeof ExchRate> | null>(null);
const ToolRef = ref<InstanceType<typeof Tool> | null>(null);
const NullRef = ref<InstanceType<typeof Null> | null>(null);

const CouponCountRef = ref<InstanceType<typeof CouponCountCard> | null>(null);

// 优惠券信息
const coupon = ref<CouponTypes.ICouponTotailRsp>({});

// 登录且绑定商户
const show = computed(() => {
  return isLogined.value && activeMerchant.value?.partnerCode;
});

const showCouponCountTip = computed(() => {
  const { waitActiveCount = 0 } = unref(coupon);
  return waitActiveCount > 0;
});

const displayText = computed(() => {
  const { waitReceiveCount, waitActiveCount, waitCount } = coupon.value;
  if (waitReceiveCount) {
    return `${waitReceiveCount + t("zhang-you-hui-quan-dai-ling-qu")}`;
  } else if (waitActiveCount) {
    return `${waitActiveCount + t("zhang-you-hui-quan-dai-ji-huo")}`;
  } else if (waitCount) {
    return `${waitCount + t("zhang-you-hui-quan-dai-shi-yong")}`;
  } else {
    return t("you-hui-quan");
  }
});

watch(
  show,
  () => {
    getData();
  },
  {
    deep: true,
    immediate: true,
  },
);

function getData() {
  if (show.value) {
    getCoupon();
    TotalRef.value?.getData();
    GridMenuRef.value?.getData();
    getOrder();
    MessageRef.value?.getData();
    ExchRateRef.value?.getData();
    CouponCountRef.value?.getData();
  }

  if (isLogined.value) {
    NullRef.value?.getData();
  }
}

// 获取优惠券信息
async function getCoupon() {
  const res = await couponApi.ApiCouponsStatisticsPost();
  coupon.value = res?.data || {};
}

// 获取待流向安排
const order = ref<AgentCustomerTypes.IOrderNoTakeVo>({});
async function getOrder() {
  const res = await agentCustomerApi.ApiOrderOrdertakecountGet();
  order.value = res.data || {};
}

function skipCoupon() {
  const url = "/coupon-h5/pages/index";
  const type = coupon.value.waitReceiveCount
    ? "WAIT_RECEIVE"
    : coupon.value.waitActiveCount
    ? "WAIT_ACTIVE"
    : "WAIT";

  openWebview({ url: `${url}?type=${type}` });
}

function onRefresh() {
  refreshHomeView(() => {
    getData();
  });
}
</script>
