<template>
  <div class="h-full f-c flex-col justify-between bg-white">
    <div class="m-auto w-80% f-c-c flex-col gap-5 pt-100rpx text-center">
      <image
        :src="COMMON_IMAGES.logo"
        mode="aspectFit"
        class="mb-50rpx size-120px"
      />

      <nut-button
        type="primary"
        block
        shape="square"
        :loading="wxLoginLoading"
        @click="wxLogin"
      >
        {{ $t("li-ji-deng-lu") }}
      </nut-button>
      <nut-button plain type="primary" block shape="square" @click="telLogin">
        {{ $t("otherlogin") }}
      </nut-button>
    </div>

    <div class="mx-30rpx mb-40rpx">
      <nut-checkbox v-model="agree">
        {{ $t("yi-yue-du-bing-tong-yi") }}
        <Protocols :protocols="loginProtocols" />
      </nut-checkbox>
      <nut-safe-area position="bottom" />
    </div>

    <nut-dialog
      v-model:visible="showProtocolDialog"
      :title="$t('wen-xin-ti-shi')"
      @ok="continueNext"
    >
      <div class="h-80rpx text-left">
        {{ $t("qing-yue-du-bing-tong-yi") }}
        <Protocols :protocols="loginProtocols" />
      </div>
    </nut-dialog>

    <nut-dialog
      v-model:visible="showGetPhoneDialog"
      :title="$t('wen-xin-ti-shi')"
    >
      {{ $t("bind-phone-tips") }}
      <template #footer>
        <div class="w-full flex justify-around">
          <nut-button
            plain
            size="small"
            type="primary"
            @click="showGetPhoneDialog = false"
          >
            {{ $t("qu-xiao") }}
          </nut-button>
          <nut-button
            size="small"
            type="primary"
            open-type="getPhoneNumber"
            @getphonenumber="bindPhoneLogin"
            @click="showGetPhoneDialog = false"
          >
            {{ $t("que-ren") }}
          </nut-button>
        </div>
      </template>
    </nut-dialog>
  </div>
</template>

<script setup lang="ts">
import type { ButtonOnGetphonenumberEvent } from "@uni-helper/uni-app-types";
import { COMMON_IMAGES } from "@/composables/helpers/preload";
import { YdtProtocols } from "@/config/protocols";

useTitle(t("deng-lu-you-ding-te"));

let redirect = "";
const agree = ref(false);
const wxLoginLoading = ref(false);
const showProtocolDialog = ref(false);
const showGetPhoneDialog = ref(false);
const loginProtocols = [YdtProtocols.register, YdtProtocols.privacy];
let next: () => void;
const { logout } = useUserStore();
const { loginSuccess } = useEntry();
const { runWxLoginTask, runBindPhoneTask } = useWxLogin();

onLoad(options => {
  if (options?.clear) {
    logout({ manually: true });
  }
  if (typeof options?.redirect === "string") {
    redirect = decodeURIComponent(options?.redirect);
  }
});

function withAgree(callback: () => void) {
  if (agree.value) {
    callback();
  } else {
    showProtocolDialog.value = true;
    next = callback;
  }
}

function wxLogin() {
  withAgree(async () => {
    try {
      wxLoginLoading.value = true;
      const wxUserInfo = await runWxLoginTask();
      if (wxUserInfo) {
        if (wxUserInfo.finishLogin) {
          loginSuccess({ redirect, withToken: true });
        } else {
          showGetPhoneDialog.value = true;
        }
      }
    } finally {
      wxLoginLoading.value = false;
    }
  });
}

function telLogin() {
  withAgree(() => {
    let url = "/pages/login/tel/index";
    if (redirect) {
      url = url.toParamsUrl({ redirect });
    }
    uni.navigateTo({ url });
  });
}

function continueNext() {
  showProtocolDialog.value = false;
  agree.value = true;
  next?.();
}

async function bindPhoneLogin(event: ButtonOnGetphonenumberEvent) {
  await runBindPhoneTask(event);
  loginSuccess({ redirect, withToken: true });
}
</script>
