<template>
  <ScrollList :state="supplyState" :empty="list.length === 0">
    <TradeCard v-for="item in list" :key="item.id" :data="item" />
  </ScrollList>
</template>

<script setup lang="ts">
import type { MatchTypes } from "@/api";
import TradeCard from "../TradeCard/index.vue";

const pagination = new Pagination();
const list = ref<MatchTypes.ProductAppRsp[]>([]);

const [fetchSupplyList, supplyState] = useListFetch({
  pagination,
  onListClear: () => (list.value = []),
  fetch: async dispatch => {
    const rsp = await matchApi.apiAppSearchSearchSupplyPurchasePost({
      pageNo: pagination.currentPage,
      pageSize: pagination.pageSize,
    });
    dispatch(() => {
      pagination.setTotal(rsp.data?.total ?? 0);
      list.value.push(...(rsp.data?.list ?? []));
    });
  },
});

defineExpose({ fetchSupplyList });
</script>
