import { <PERSON>Header } from "@/api/constant";
import { Config } from "@/config";
import { parseUrl } from "@/utils/url";
import { pick } from "lodash";

interface GlobalWebviewOption {
  autoOrigin?: boolean;
  withToken?: boolean;
  tokenField?: string;
}
interface OpenWebviewOption extends GlobalWebviewOption {
  title?: string;
  url: string;
  isRedirect?: boolean;
}

interface UseNavigateConfig {
  webview?: GlobalWebviewOption;
}

interface GuardOption {
  when?: () => boolean;
  returnTo?: boolean;
}

type PageRoute = WechatMiniprogram.Page.Instance<
  WechatMiniprogram.IAnyObject,
  WechatMiniprogram.IAnyObject
> & {
  path: NavigateToOptions["url"];
};

export function useNavigate(topConfig: UseNavigateConfig = {}) {
  const { isLogined, token, userInfo } = storeToRefs(useUserStore());

  function getCurrentRoute() {
    const pages = getCurrentPages();
    const current = pages[pages.length - 1];
    return {
      ...current,
      path: `/${current.route}` as NavigateToOptions["url"],
    } as PageRoute;
  }

  // 跳转到实名页
  function toRealNamePage() {
    const { realnameAuthStatus } = userInfo.value ?? {};
    if (realnameAuthStatus === "UN_AUTH") {
      uni.navigateTo({ url: "/subpages/realname/form" });
    } else {
      uni.navigateTo({ url: "/subpages/realname/info" });
    }
  }

  // 实名判断钩子
  function withRealName(options?: GuardOption) {
    return {
      run: (callback: () => void) => {
        const condition =
          typeof options?.when === "function" ? options.when() : true;
        if (!condition) return;

        if (!isLogined.value) {
          const url = getReturnToLoginUrl(options);
          uni.navigateTo({ url });
          return;
        }

        if (userInfo.value?.realnameAuthStatus !== "SUCCESS") {
          showConfirm(t("renzhengtishi")).then(toRealNamePage);
          return;
        }

        callback();
      },
    };
  }

  // 登录判断钩子
  function withLogin(options?: GuardOption) {
    return {
      run: (callback: () => void) => {
        if (
          isLogined.value ||
          (typeof options?.when === "function" && !options.when())
        ) {
          callback();
          return;
        }

        const url = getReturnToLoginUrl(options);
        uni.redirectTo({ url });
      },
    };
  }

  function getReturnToLoginUrl(options?: GuardOption) {
    let loginUrl = "/pages/login/index";
    if (options?.returnTo) {
      const { route, $page } = getCurrentRoute();
      const url =
        options.returnTo === true ? $page.fullPath || route : options.returnTo;
      const redirect = url.startsWith("/") ? url : `/${url}`;
      loginUrl = loginUrl.toParamsUrl({
        redirect: decodeURIComponent(redirect),
      });
    }
    return loginUrl;
  }

  // 打开webview
  function openWebview(options: OpenWebviewOption) {
    const webviewUrl = generatorWebviewUrl(options);
    const navigateMethod = options.isRedirect ? uni.redirectTo : uni.navigateTo;
    navigateMethod({
      url: webviewUrl,
    });
  }

  function generatorWebviewUrl(options: OpenWebviewOption) {
    const shouldWithToken: boolean =
      options.withToken ?? topConfig?.webview?.withToken ?? false;
    const tokenField: string =
      options.tokenField ?? topConfig?.webview?.tokenField ?? XHeader.HToken;
    const autoOrigin: boolean =
      options.autoOrigin ?? topConfig.webview?.autoOrigin ?? false;

    if (autoOrigin && !options.url.startsWith("http")) {
      options.url = Config.baseUrl + options.url;
    }

    const params = pick(options, ["url", "title"]);
    if (shouldWithToken && isLogined.value) {
      params.url = params.url.toParamsUrl({
        [tokenField]: token.value,
      });
    }

    return "/pages/webview/index".toParamsUrl(params);
  }

  // 重定向到首页
  function redirectToIndex() {
    uni.redirectTo({
      url: "/pages/index/index",
    });
  }

  // 通用链接跳转
  function unknownUrlNavigate(url?: string) {
    if (!url) return;
    const { query } = parseUrl(url);
    const shouldLogin = query.shouldLogin === "1";

    withLogin({ when: () => shouldLogin }).run(() => {
      if (url.startsWith("http")) {
        openWebview({
          url,
          withToken: shouldLogin,
        });
        return;
      }

      uni.redirectTo({
        url,
      });
    });
  }

  const dependencies = {
    withLogin,
    openWebview,
  };
  const gridAppNavigate = useGridAppNavigate(dependencies);

  return {
    toRealNamePage,
    withRealName,
    redirectToIndex,
    unknownUrlNavigate,
    getCurrentRoute,
    generatorWebviewUrl,
    ...dependencies,
    ...gridAppNavigate,
  };
}
