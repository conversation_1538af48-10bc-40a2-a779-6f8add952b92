export enum HomeTabEnum {
  Mall,
  Porxy,
}

export function useHomeView() {
  const searchBarHeight = "120rpx";
  const { menuButtonBounding } = storeToRefs(useAppStore());
  const refreshing = ref(false);

  const scrollViewHeight = computed(
    () =>
      `calc(100vh - ${searchBarHeight} - ${
        menuButtonBounding.value?.height ?? 0
      }px - ${menuButtonBounding.value?.top ?? 0}px - ${
        nutTheme.tabbarHeight
      } - env(safe-area-inset-bottom))`,
  );

  function refreshHomeView(callback: () => void) {
    refreshing.value = true;
    setTimeout(() => {
      refreshing.value = false; // 固定时间改变刷新状态，不等接口响应
    }, 2000);

    callback();
  }

  return {
    searchBarHeight,
    scrollViewHeight,
    refreshing,
    refreshHomeView,
  };
}
