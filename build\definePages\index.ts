import { existsSync } from "node:fs";
import path from "node:path";
import process from "node:process";
import { getRootPath } from "../utils";

export function getPageConfigSource() {
  const VITE_BUILD_MODE = process.env?.VITE_BUILD_MODE;

  if (VITE_BUILD_MODE) {
    // 判断build/definePages/${VITE_BUILD_MODE}.ts文件是否存在
    const targetPath = path.resolve(
      getRootPath(),
      "build/definePages",
      `${VITE_BUILD_MODE}.config.ts`,
    );

    if (existsSync(targetPath)) {
      return targetPath;
    }
  }

  return undefined;
}
