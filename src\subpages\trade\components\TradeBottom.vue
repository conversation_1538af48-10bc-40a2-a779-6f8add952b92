<template>
  <div class="fixed bottom-0 left-0 right-0">
    <div
      v-if="showSnackBar"
      class="mx-auto mb-5px w-92% f-c justify-between rounded-12px bg-[#31353e] bg-opacity-80 p-8px text-26rpx color-white"
    >
      <div class="f-c gap-12px font-400">
        <image
          class="size-26px"
          :src="'mall_match/icon/logo.png'.toQiniuUrl({ suffixPath: '' })"
        />
        登录后价格可见
      </div>

      <div class="login-btn f-c justify-between gap-2px">
        <nut-button
          type="primary"
          size="small"
          @click="withLogin({ returnTo: true }).run(() => {})"
        >
          一键登录
        </nut-button>
        <image
          class="size-28px"
          :src="
            'mall_match/icon/close-icon-white.png'.toQiniuUrl({
              suffixPath: '',
            })
          "
          @click="showSnackBar = false"
        />
      </div>
    </div>

    <div class="bg-white py-20rpx">
      <div class="mx-auto w-90%">
        <nut-button
          shape="square"
          size="large"
          type="primary"
          @click="makePhoneCall"
        >
          电话联系
        </nut-button>
      </div>
      <nut-safe-area position="bottom" />
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(["callPhone"]);
const { isLogined } = storeToRefs(useUserStore());
const { withLogin, withRealName } = useNavigate();
const showSnackBar = ref(!isLogined.value);

function makePhoneCall() {
  withRealName().run(() => {
    emit("callPhone");
  });
}
</script>

<style lang="scss" scoped>
.login-btn {
  --nut-button-primary-background-color: linear-gradient(
    315deg,
    rgb(73 143 242) 0%,
    rgb(73 101 242) 100%
  );
}
</style>
