import type { MatchTypes } from "@/api";

const DEFAULT_CATEGORY_IMAGE: Record<string, string> = {
  MEAT_BEEF_IMPORT: "mall_match/icon/cattle.png",
  MEAT_CHICKEN_IMPORT: "mall_match/icon/chicken.png",
  MEAT_DUCK_IMPORT: "mall_match/icon/duck.png",
  MEAT_PORK_IMPORT: "mall_match/icon/pig.png",
  AQUATIC_PRODUCTS_IMPORT: "mall_match/icon/sea_food.png",
};

export interface SupplyThumbnail {
  url: string;
  titles: string[];
}

export function useSupplyImage(
  data: MatchTypes.ProductAppRsp | MatchTypes.SupplyProductAppRsp,
) {
  // 详情页轮播图
  const gallery: Array<SupplyThumbnail | string> = [];

  // 列表缺省图对象
  const thumbnail: SupplyThumbnail = {
    url: "",
    titles: [],
  };

  thumbnail.url =
    (typeof data.supplyImageMain === "string"
      ? data.supplyImageMain
      : data.supplyImageMain?.fileKey) ?? "";
  if (!thumbnail.url) {
    thumbnail.url = data.firstLevel
      ? DEFAULT_CATEGORY_IMAGE[data.firstLevel] ?? ""
      : "";
    thumbnail.titles.push(data.threeLevelsName ?? "");
    thumbnail.titles.push(data.originCountry ?? "");
    thumbnail.titles.push(data.brand ?? "");
  }

  if (isSupplyType(data)) {
    if (data.supplyImageMain?.fileKey) {
      gallery.push(data.supplyImageMain.fileKey);
    }

    if (data.supplyImage?.length) {
      gallery.push(...data.supplyImage.map(image => image.fileKey ?? ""));
    }

    if (gallery.length === 0) {
      gallery.push(thumbnail);
    }
  }

  return {
    thumbnail,
    gallery,
  };
}
