<template>
  <Detail :detail-data="detailData" />
</template>

<script setup lang="ts">
import type { IArticleVo } from "news-api/src/queryBackend/types";
import shareReport from "@/static/share-report.png";
import Detail from "./components/Detail.vue";

const detailData = ref<IArticleVo>({});

async function queryDetail(id: string) {
  if (id) {
    const rsp = await queryBackendApi.ApiQuerytoolNewsqueryNewsdetailGet(
      {
        id: Number(id),
      },
      {
        showLoading: true,
      },
    );
    detailData.value = rsp.data;
  }
}

onLoad(options => {
  queryDetail(options?.id);
});

onShareAppMessage(options =>
  useShareAppMessage(options, { useCurrentPath: true }),
);

onShareTimeline(() => {
  return {
    title: detailData.value?.contentTitle,
    query: `id=${detailData.value?.id}`,
    imageUrl: shareReport,
  };
});
</script>

<style scoped></style>
