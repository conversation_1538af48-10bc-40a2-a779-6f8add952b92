<template>
  <div class="flex pb-80rpx">
    <div class="mr-12rpx flex-1 rounded-16rpx bg-white px-24rpx py-20rpx">
      <div class="text-30rpx color-black lh-42rpx">
        {{ $t("zhao-gong-ju") }}
      </div>
      <div
        v-for="(item, index) in tool"
        :key="index"
        class="relative mt-16rpx box-border h-90rpx rounded-8rpx from-#FFF7EF to-#FFFCF9 bg-gradient-to-b px-20rpx py-20rpx"
        @click="() => handleGridAppNavigate(item)"
      >
        <div class="text-26rpx">{{ item.functionName }}</div>
        <image
          :src="item.imageUrl"
          class="absolute right-16rpx top--6rpx h-[80rpx] w-[80rpx]"
        />
      </div>
    </div>
    <div class="flex-1 rounded-16rpx bg-white px-24rpx py-20rpx">
      <div class="text-30rpx color-black lh-42rpx">
        {{ $t("cha-xin-xi") }}
      </div>
      <div
        v-for="(item, index) in info"
        :key="index"
        class="relative mt-16rpx box-border h-90rpx rounded-8rpx from-#F3F8FE to-#FAFCFF bg-gradient-to-b px-20rpx py-20rpx"
        @click="() => handleGridAppNavigate(item)"
      >
        <div class="text-26rpx">{{ item.functionName }}</div>
        <image
          :src="item.imageUrl"
          class="absolute right-16rpx top--6rpx h-[80rpx] w-[80rpx]"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { PlatformTypes } from "@/api/index";
import { GridAppPosition, GridAppType } from "@/enums";

const { openWebview } = useNavigate({
  webview: { withToken: true, autoOrigin: true },
});
const { handleGridAppNavigate } = useNavigate();
const KEY_PROXY_TOOL_GRID = "proxyTool";
const KEY_PROXY_INFO_GRID = "proxyInfo";
const tool = ref<PlatformTypes.IAppFunctionByTypeVo[]>([]);
const info = ref<PlatformTypes.IAppFunctionByTypeVo[]>([]);

const [fetchToolList] = useApiFetch(() =>
  platformApi
    .ApiAppnewFunctionsGet({
      clientType: GridAppType.miniProgram,
      showPosition: GridAppPosition.agentTool,
    })
    .then(async rsp => {
      tool.value = rsp.data;
      uni.setStorageSync(KEY_PROXY_TOOL_GRID, JSON.stringify(tool.value));
    }),
);

const [fetchInfoList] = useApiFetch(() =>
  platformApi
    .ApiAppnewFunctionsGet({
      clientType: GridAppType.miniProgram,
      showPosition: GridAppPosition.agentMessage,
    })
    .then(async rsp => {
      info.value = rsp.data;
      uni.setStorageSync(KEY_PROXY_INFO_GRID, JSON.stringify(tool.value));
    }),
);

function tryInitToolStroage(): PlatformTypes.IPlatformFrontAppItemVo[] {
  const storageData = uni.getStorageSync(KEY_PROXY_TOOL_GRID);
  if (storageData) {
    try {
      tool.value = JSON.parse(storageData);
    } catch {}
  }

  return [];
}
function tryInitInfoStroage(): PlatformTypes.IPlatformFrontAppItemVo[] {
  const storageData = uni.getStorageSync(KEY_PROXY_INFO_GRID);
  if (storageData) {
    try {
      info.value = JSON.parse(storageData);
    } catch {}
  }

  return [];
}

onBeforeMount(() => {
  tryInitToolStroage();
  tryInitInfoStroage();
  fetchToolList();
  fetchInfoList();
});
</script>
