import { parseStreamingData, resetStreamingParser } from "./useParseStreaming";

/**
 * 调试版本的流式解析器
 * 用于分析实际的数据流问题
 */
export function debugStreamingParser(
  chunks: string[],
  isLastChunk: boolean[] = [],
) {
  console.log("=== 调试流式解析器 ===");
  console.log(`总共 ${chunks.length} 个chunk`);

  // 重置解析器
  resetStreamingParser();

  const allResults: any[] = [];

  chunks.forEach((chunk, index) => {
    const isComplete = isLastChunk[index] || index === chunks.length - 1;

    console.log(`\n--- 处理 chunk ${index + 1} ---`);
    console.log(`长度: ${chunk.length}`);
    console.log(`是否最后一个: ${isComplete}`);
    console.log(`前100字符: ${chunk.substring(0, 100)}...`);

    // 检查chunk是否包含data:前缀
    const hasDataPrefix = chunk.trim().startsWith("data:");
    console.log(`包含data:前缀: ${hasDataPrefix}`);

    // 检查chunk是否包含完整的JSON结构
    const openBraces = (chunk.match(/\{/g) || []).length;
    const closeBraces = (chunk.match(/\}/g) || []).length;
    console.log(`开括号数量: ${openBraces}, 闭括号数量: ${closeBraces}`);

    try {
      // 使用自动检测，不传递isComplete参数
      const result = parseStreamingData(chunk);
      console.log(`解析结果: ${result.length} 个对象`);

      result.forEach((obj: any, objIndex) => {
        console.log(`  对象 ${objIndex + 1}:`, {
          id: `${obj.message?.id?.substring(0, 8)}...`,
          event_type: obj.event_type,
          has_content: !!obj.message?.content,
          has_action_content: !!obj.message?.content?.action_content,
          action_content_length:
            obj.message?.content?.action_content?.length || 0,
        });
      });

      allResults.push(...result);
    } catch (error) {
      console.error(`chunk ${index + 1} 解析出错:`, (error as Error).message);
    }
  });

  console.log("\n=== 最终结果 ===");
  console.log(`总共解析到 ${allResults.length} 个对象`);

  // 分析结果
  const withActionContent = allResults.filter(
    obj => obj.message?.content?.action_content?.length > 0,
  );
  const withoutActionContent = allResults.filter(
    obj =>
      !obj.message?.content?.action_content ||
      obj.message.content.action_content.length === 0,
  );

  console.log(`包含action_content的对象: ${withActionContent.length}`);
  console.log(`不包含action_content的对象: ${withoutActionContent.length}`);

  // 如果只有一个对象，分析可能的原因
  if (allResults.length === 1) {
    console.log("\n=== 只有一个对象的可能原因分析 ===");
    const obj = allResults[0];

    if (obj.message?.content?.action_content?.length > 0) {
      console.log("✓ 对象包含完整的action_content数组");
      console.log(`  数组长度: ${obj.message.content.action_content.length}`);
      console.log(
        `  第一项: ${JSON.stringify(
          obj.message.content.action_content[0],
          null,
          2,
        ).substring(0, 200)}...`,
      );
    } else {
      console.log("✗ 对象不包含action_content或为空");
    }

    console.log("可能原因:");
    console.log("1. 第二个data:块被解析器跳过了");
    console.log("2. 第二个data:块格式有问题");
    console.log("3. 解析器逻辑有bug");
  }

  return allResults;
}

/**
 * 使用你的实际数据进行测试
 */
export function testWithYourData() {
  // 使用你提供的原始数据格式
  const chunk1 = `data: {
  message: {
    id: "6a6222b5-5e9b-43e6-bb39-0f3920a3b247",
    session_id: "3271",
    message_id: "5236",
    intention_id: "4924",
    event_type: "node_finished",
    event_time: "2025-09-11 16:17:51",
    content: {
      action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1",
      action_content: [
        {
          orderId: "4500013082",
          cabinetNo: "MNBU4080965",
          contract: "901/2022（3101）",
          stockWeight: "19986.801",
          shelfStartExpDate: "2024/09/28",
          prodStartDate: "2022/09/29",
          prodEndDate: "2022/10/14",
          declared: "",
          finalVoyageEta: "",
          portName: "",
          transferHarbourDate: "",
          transitHarbourName: "",
          inboundNo: "ZR00003624",
          warehouseName: "深圳锋润锋冷库（同乐）",
          stockPiece: 924,`;

  const chunk2 = `   stockGrossWeight: "20584.626",
          countryName: "巴西",
          factoryCode: "SIF2146",
          goodsName: "冷冻五花肉条",
          pieceNum: 924,
          materialDesc: "冷冻去骨猪肉块",
          shelfDays: -333,
          orderStatus: "",
          shelfEndExpDate: "2024/10/13",
        },
        {
          orderId: "4500013082",
          cabinetNo: "MNBU4080965",
          contract: "901/2022（3101）",
          stockWeight: "19986.801",
          shelfStartExpDate: "2024/09/28",
          prodStartDate: "2022/09/29",
          prodEndDate: "2022/10/14",
          declared: "",
          finalVoyageEta: "",
          portName: "",
          transferHarbourDate: "",
          transitHarbourName: "",
          inboundNo: "ZR00003624",
          warehouseName: "深圳锋润锋冷库（同乐）",
          stockPiece: 924,
          stockGrossWeight: "20584.626",
          countryName: "巴西",`;

  const chunk3 = `  factoryCode: "SIF2146",
          goodsName: "冷冻五花肉条",
          pieceNum: 924,
          materialDesc: "冷冻去骨猪肉块",
          shelfDays: -333,
          orderStatus: "",
          shelfEndExpDate: "2024/10/13",
        },
      ],
      total: 300,
      ai_search_key: "",
      user_input: "查询我的库存",
      is_card: 1,
      card_type: "stockGoodsCard",
      status: "succeed",
    },
    plan_id: "3947",
    action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1",
    duration: 2.0,
  },
  event_type: "node_finished",
}

data: {
  message: {
    id: "6a6222b5-5e9b-43e6-bb39-0f3920a3b247",
    session_id: "3271",
    message_id: "5236",
    intention_id: "4924",
    event_type: "node_finished",
    event_time: "2025-09-11 16:17:51",
    content: {
      action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1",
      total: 300,
      ai_search_key: "",
      user_input: "查询我的库存",
      is_card: 1,
      card_type: "stockGoodsCard",
      status: "succeed",
    },
    plan_id: "3947",
    action_id: "6ac7cc72-a10b-4319-806e-bc111d32afd1",
    duration: 2.0,
  },
  event_type: "node_finished",
};`;

  return debugStreamingParser([chunk1, chunk2, chunk3]); // 不再需要指定isComplete
}

// 运行测试
testWithYourData();
