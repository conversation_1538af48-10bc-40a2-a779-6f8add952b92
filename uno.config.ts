import type { PresetOrFactory, UserConfig } from "unocss";
import { isH5 } from "@uni-helper/uni-env";
import { presetUni } from "@uni-helper/unocss-preset-uni";
import {
  defineConfig,
  presetIcons,
  transformerDirectives,
  transformerVariantGroup,
} from "unocss";
import { unotheme } from "./src/styles/themes/uno.theme";

const darkMode = isH5 ? "class" : "media";
const config: UserConfig = defineConfig({
  rules: [
    [
      /^bgi-\[([\s\S]+)\]$/,
      ([, d]) => {
        const path = "@/static";
        return { "background-image": `url('${path}/${d}')` };
      },
    ],
    [
      /^(solid|dashed|dotted)-(\d+)rpx-(.*)$/, // 正则表达式匹配类名
      ([, style, width, color]) => ({
        border: `${width}rpx ${style} ${color}`, // 动态生成边框样式
      }),
    ],
  ],
  content: {
    pipeline: {
      exclude: [
        "node_modules",
        ".git",
        ".github",
        ".husky",
        ".vscode",
        "build",
        "dist",
        "mock",
        "public",
        "types",
        "./stats.html",
      ],
    },
  },
  shortcuts: [
    ["wh-full", "w-full h-full"],
    ["f-c-c", "flex justify-center items-center"],
    ["f-c", "flex items-center"],
    ["m-auto", "m-0 mx-auto"],
    ["flex-col", "flex flex-col"],
    ["absolute-lt", "absolute left-0 top-0"],
    ["absolute-lb", "absolute left-0 bottom-0"],
    ["absolute-rt", "absolute right-0 top-0"],
    ["absolute-rb", "absolute right-0 bottom-0"],
    ["absolute-center", "absolute-lt f-c-c wh-full"],
    ["text-ellipsis", "truncate"],
    ["rounded", "rounded-[16rpx]"],
    ["rounded-card", "bg-white rounded mt-[16rpx] mb-[16rpx]"],
    ["card-title", "color-black-bold text-30rpx font-500"],
    ["circle", "rounded-full"],
    [
      "grid-menu-card",
      "mt-0 rounded-card flex flex-wrap justify-between p-[16rpx]",
    ],
  ],
  presets: [
    presetUni({
      uno: {
        dark: darkMode,
        variablePrefix: "li-",
      },
      attributify: {
        ignoreAttributes: ["block", "fixed"],
      },
    }) as PresetOrFactory,
    presetIcons({
      scale: 1.2,
      warn: true,
      extraProperties: {
        display: "inline-block",
        "vertical-align": "middle",
      },
    }),
  ],
  transformers: [transformerDirectives(), transformerVariantGroup()],
  safelist: "prose prose-sm m-auto text-left".split(" "),
  theme: unotheme,
});

export default config;
