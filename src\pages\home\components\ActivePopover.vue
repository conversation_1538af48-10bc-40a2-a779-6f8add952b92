<template>
  <view
    v-if="showDialog"
    class="fixed left-0 top-0 z-999 h-full w-full flex flex-col items-center justify-center bg-[rgba(0,0,0,.7)]"
  >
    <image
      class="block h-auto w-full"
      :src="popup.imgUrl"
      mode="widthFix"
      @click="goActivity"
    />
    <image
      class="mt-50rpx h-88rpx w-88rpx"
      src="~@/static/close.png"
      mode="widthFix"
      @click="closeActivity()"
    />
  </view>
</template>

<script lang="ts" setup>
import type { PlatformTypes } from "@/api";
import { AuthConfig } from "@/config";
import { formatDate } from "@/utils/date";

const userStore = useUserStore();
const { isLogined } = storeToRefs(userStore);

const { openWebview } = useNavigate({
  webview: { withToken: true, autoOrigin: true },
});

const showActivity = ref(true); // 是否展示活动弹窗
const showGift = ref(false); // 是否展示大礼包弹窗
const flag = ref(false); // 是否填写过资料

const popup = ref<PlatformTypes.IPlatformBannerVo>({}); // 弹窗数据

// 是否弹窗
const showDialog = computed(() => {
  return (
    (showActivity.value || showGift.value) &&
    popup.value.status === "ON" &&
    !!popup.value.imgUrl
  );
});

onBeforeMount(() => {
  showActivity.value = !uni.getStorageSync("CloseActivityTime");
  if (showActivity.value) {
    getActivity();
  }

  const CloseGiftTime = uni.getStorageSync("CloseGiftTime") || "";
  showGift.value = isLogined.value && CloseGiftTime !== formatDate();
  if (showGift.value) {
    getGiftPack();
  }
});

// 获取活动弹窗
async function getActivity() {
  const type = isLogined.value ? "FIRST_AD_LOGIN" : "FIRST_AD_UNLOGIN";
  const res = await platformApi.ApiBannerListbypositionPost({
    appId: AuthConfig.appId,
    showPositions: [type],
  });
  const list =
    res.data.find(item => item.showPosition === type)?.bannerList ?? [];
  popup.value = list?.[0] ?? {};
}

// 获取大礼包弹窗
async function getGiftPack() {
  // 获取大礼包数据
  const coupon = await couponApi.ApiCouponsWaitingclaimedcouponsGet();

  if (coupon.data && coupon.data.length) {
    // 是否填过资料
    const rsp = await couponApi.ApiMtablesQueryflagGet();
    flag.value = rsp.data || false;

    const res = await platformApi.ApiBannerListbypositionPost({
      appId: AuthConfig.appId,
      showPositions: ["OLD_CUS_COUPON"],
    });
    const list =
      res.data.find(item => item.showPosition === "OLD_CUS_COUPON")
        ?.bannerList ?? [];
    popup.value = list?.[0] ?? {};
  }
}

// 弹窗跳转
function goActivity() {
  const link =
    popup.value.showPosition === "OLD_CUS_COUPON" && !flag.value
      ? "/coupon-h5/pages/fillIn?isClaim=true"
      : popup.value.link;

  closeActivity();

  if (!link) return;
  openWebview({ url: link });
}

// 关闭弹窗
function closeActivity() {
  uni.setStorageSync(
    popup.value.showPosition === "OLD_CUS_COUPON"
      ? "CloseGiftTime"
      : "CloseActivityTime",
    formatDate(),
  );

  popup.value.imgUrl = undefined;
}
</script>
